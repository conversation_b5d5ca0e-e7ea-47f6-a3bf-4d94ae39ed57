import { useState } from 'react'
import { 
  Plus, 
  <PERSON>, 
  <PERSON>, 
  <PERSON>Off, 
  Edit, 
  Trash2, 
  <PERSON>, 
  Clock,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>,
  Filter,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

interface Secret {
  id: string
  name: string
  description?: string
  scope: 'global' | 'project' | 'environment'
  scopeValue?: string
  type: 'password' | 'token' | 'certificate' | 'key-value'
  lastModified: Date
  expiresAt?: Date
  isExpired: boolean
  createdBy: string
  usageCount: number
}

export function SecretsManager() {
  const { toast } = useToast()
  const [secrets, setSecrets] = useState<Secret[]>([
    {
      id: '1',
      name: 'DATABASE_PASSWORD',
      description: 'Production database password',
      scope: 'environment',
      scopeValue: 'production',
      type: 'password',
      lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30), // 30 days from now
      isExpired: false,
      createdBy: '<EMAIL>',
      usageCount: 45
    },
    {
      id: '2',
      name: 'AWS_ACCESS_KEY',
      description: 'AWS access key for deployment',
      scope: 'global',
      type: 'token',
      lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 90), // 90 days from now
      isExpired: false,
      createdBy: '<EMAIL>',
      usageCount: 128
    },
    {
      id: '3',
      name: 'SSL_CERTIFICATE',
      description: 'SSL certificate for HTTPS',
      scope: 'project',
      scopeValue: 'web-app',
      type: 'certificate',
      lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
      expiresAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5), // Expired 5 days ago
      isExpired: true,
      createdBy: '<EMAIL>',
      usageCount: 12
    },
    {
      id: '4',
      name: 'SLACK_WEBHOOK',
      description: 'Webhook URL for Slack notifications',
      scope: 'global',
      type: 'token',
      lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isExpired: false,
      createdBy: '<EMAIL>',
      usageCount: 67
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [scopeFilter, setScopeFilter] = useState<string>('all')
  const [showExpired, setShowExpired] = useState(true)
  const [visibleSecrets, setVisibleSecrets] = useState<Set<string>>(new Set())

  const filteredSecrets = secrets.filter(secret => {
    const matchesSearch = secret.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         secret.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesScope = scopeFilter === 'all' || secret.scope === scopeFilter
    const matchesExpired = showExpired || !secret.isExpired
    
    return matchesSearch && matchesScope && matchesExpired
  })

  const toggleSecretVisibility = (secretId: string) => {
    const newVisible = new Set(visibleSecrets)
    if (newVisible.has(secretId)) {
      newVisible.delete(secretId)
    } else {
      newVisible.add(secretId)
    }
    setVisibleSecrets(newVisible)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied",
        description: "Secret value copied to clipboard.",
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteSecret = (secretId: string) => {
    setSecrets(secrets.filter(s => s.id !== secretId))
    toast({
      title: "Secret Deleted",
      description: "Secret has been permanently removed.",
    })
  }

  const getScopeColor = (scope: string) => {
    switch (scope) {
      case 'global':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'project':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'environment':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'password':
        return <Key className="h-4 w-4" />
      case 'token':
        return <Shield className="h-4 w-4" />
      case 'certificate':
        return <Shield className="h-4 w-4" />
      case 'key-value':
        return <Key className="h-4 w-4" />
      default:
        return <Key className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Secrets Manager</h1>
          <p className="text-muted-foreground">
            Securely manage credentials, tokens, and sensitive configuration.
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Secret
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Secrets</p>
              <p className="text-2xl font-bold">{secrets.length}</p>
            </div>
            <Key className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Expired</p>
              <p className="text-2xl font-bold text-red-500">
                {secrets.filter(s => s.isExpired).length}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
              <p className="text-2xl font-bold text-yellow-500">
                {secrets.filter(s => 
                  s.expiresAt && 
                  !s.isExpired && 
                  s.expiresAt.getTime() < Date.now() + 1000 * 60 * 60 * 24 * 7
                ).length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Usage</p>
              <p className="text-2xl font-bold">
                {secrets.reduce((sum, s) => sum + s.usageCount, 0)}
              </p>
            </div>
            <Shield className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search secrets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          />
        </div>

        <select
          value={scopeFilter}
          onChange={(e) => setScopeFilter(e.target.value)}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <option value="all">All Scopes</option>
          <option value="global">Global</option>
          <option value="project">Project</option>
          <option value="environment">Environment</option>
        </select>

        <Button
          variant={showExpired ? "default" : "outline"}
          onClick={() => setShowExpired(!showExpired)}
        >
          <Filter className="mr-2 h-4 w-4" />
          {showExpired ? "Hide Expired" : "Show Expired"}
        </Button>
      </div>

      {/* Secrets List */}
      <div className="rounded-lg border bg-card">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Secrets</h2>
          <div className="space-y-4">
            {filteredSecrets.map((secret) => (
              <div key={secret.id} className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-accent">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(secret.type)}
                    {secret.isExpired && <AlertTriangle className="h-4 w-4 text-red-500" />}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium">{secret.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getScopeColor(secret.scope)}`}>
                        {secret.scope}
                        {secret.scopeValue && `: ${secret.scopeValue}`}
                      </span>
                    </div>
                    {secret.description && (
                      <p className="text-sm text-muted-foreground">{secret.description}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                      <span>Modified: {formatDate(secret.lastModified)}</span>
                      {secret.expiresAt && (
                        <span className={secret.isExpired ? "text-red-500" : ""}>
                          Expires: {formatDate(secret.expiresAt)}
                        </span>
                      )}
                      <span>Used {secret.usageCount} times</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => toggleSecretVisibility(secret.id)}
                  >
                    {visibleSecrets.has(secret.id) ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => copyToClipboard('***hidden***')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteSecret(secret.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
