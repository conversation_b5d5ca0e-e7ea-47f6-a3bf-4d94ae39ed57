import { memo } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { 
  GitBranch, 
  Package, 
  TestTube, 
  Server, 
  Database, 
  Mail, 
  Webhook,
  FileText,
  Shield,
  Zap,
  Docker,
  Cloud,
  CheckCircle,
  XCircle,
  Clock,
  Play
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface PipelineNodeData {
  label: string
  type: string
  config: Record<string, any>
  status?: 'idle' | 'running' | 'success' | 'failed' | 'pending'
}

const nodeIcons: Record<string, any> = {
  git: GitBranch,
  build: Package,
  test: TestTube,
  'deploy-server': Server,
  'deploy-cloud': Cloud,
  'deploy-k8s': Server,
  database: Database,
  email: Mail,
  slack: Mail,
  webhook: Webhook,
  'webhook-notify': Webhook,
  script: FileText,
  security: Shield,
  docker: Docker,
  schedule: Zap,
  condition: Zap,
  parallel: Zap,
}

const statusIcons = {
  idle: null,
  pending: Clock,
  running: Play,
  success: CheckCircle,
  failed: XCircle,
}

const statusColors = {
  idle: 'border-border',
  pending: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950',
  running: 'border-blue-500 bg-blue-50 dark:bg-blue-950',
  success: 'border-green-500 bg-green-50 dark:bg-green-950',
  failed: 'border-red-500 bg-red-50 dark:bg-red-950',
}

export const PipelineNode = memo(({ data, selected }: NodeProps<PipelineNodeData>) => {
  const Icon = nodeIcons[data.type] || Package
  const StatusIcon = data.status ? statusIcons[data.status] : null
  const statusColor = data.status ? statusColors[data.status] : statusColors.idle

  return (
    <div
      className={cn(
        "px-4 py-3 shadow-md rounded-lg bg-card border-2 min-w-[150px]",
        statusColor,
        selected && "ring-2 ring-primary ring-offset-2"
      )}
    >
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-muted-foreground border-2 border-background"
      />
      
      <div className="flex items-center space-x-2">
        <Icon className="h-5 w-5 text-muted-foreground" />
        <div className="flex-1">
          <div className="text-sm font-medium text-foreground">{data.label}</div>
          <div className="text-xs text-muted-foreground capitalize">{data.type}</div>
        </div>
        {StatusIcon && (
          <StatusIcon 
            className={cn(
              "h-4 w-4",
              data.status === 'running' && "animate-spin",
              data.status === 'success' && "text-green-500",
              data.status === 'failed' && "text-red-500",
              data.status === 'pending' && "text-yellow-500",
              data.status === 'running' && "text-blue-500"
            )} 
          />
        )}
      </div>

      {/* Configuration preview */}
      {Object.keys(data.config).length > 0 && (
        <div className="mt-2 pt-2 border-t border-border">
          <div className="text-xs text-muted-foreground">
            {Object.entries(data.config).slice(0, 2).map(([key, value]) => (
              <div key={key} className="truncate">
                <span className="font-medium">{key}:</span> {String(value)}
              </div>
            ))}
            {Object.keys(data.config).length > 2 && (
              <div className="text-muted-foreground/60">
                +{Object.keys(data.config).length - 2} more...
              </div>
            )}
          </div>
        </div>
      )}

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-muted-foreground border-2 border-background"
      />
    </div>
  )
})
