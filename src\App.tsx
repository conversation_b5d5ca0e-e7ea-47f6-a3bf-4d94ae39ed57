import { Routes, Route } from 'react-router-dom'
import { Toaster } from '@/components/ui/toaster'
import { ThemeProvider } from '@/components/theme-provider'
import { Layout } from '@/components/layout/Layout'
import { Dashboard } from '@/pages/Dashboard'
import { PipelineDesigner } from '@/pages/PipelineDesigner'
import { DeploymentTargets } from '@/pages/DeploymentTargets'
import { SecretsManager } from '@/pages/SecretsManager'
import { Templates } from '@/pages/Templates'
import { Settings } from '@/pages/Settings'

function App() {
  return (
    <ThemeProvider defaultTheme="system" storageKey="pipeline-platform-theme">
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/designer" element={<PipelineDesigner />} />
          <Route path="/designer/:pipelineId" element={<PipelineDesigner />} />
          <Route path="/deployments" element={<DeploymentTargets />} />
          <Route path="/secrets" element={<SecretsManager />} />
          <Route path="/templates" element={<Templates />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Layout>
      <Toaster />
    </ThemeProvider>
  )
}

export default App
