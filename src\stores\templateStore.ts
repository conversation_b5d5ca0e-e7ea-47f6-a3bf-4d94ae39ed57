import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Template, TemplateParameter, TemplateReview, Pipeline } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

interface TemplateState {
  templates: Template[]
  selectedTemplate: Template | null
  isLoading: boolean
  
  // Actions
  createTemplate: (template: Omit<Template, 'id' | 'createdAt' | 'updatedAt' | 'downloads' | 'rating' | 'reviews'>) => Template
  updateTemplate: (id: string, updates: Partial<Template>) => void
  deleteTemplate: (id: string) => void
  selectTemplate: (template: Template | null) => void
  
  // Template operations
  instantiateTemplate: (templateId: string, parameters: Record<string, any>) => Pipeline
  publishTemplate: (templateId: string) => Promise<void>
  unpublishTemplate: (templateId: string) => Promise<void>
  
  // Reviews and ratings
  addReview: (templateId: string, review: Omit<TemplateReview, 'id' | 'createdAt'>) => void
  updateRating: (templateId: string) => void
  
  // Search and filtering
  searchTemplates: (query: string) => Template[]
  getTemplatesByCategory: (category: string) => Template[]
  getTemplatesByAuthor: (author: string) => Template[]
  getPopularTemplates: (limit?: number) => Template[]
  
  // Import/Export
  exportTemplate: (templateId: string) => string
  importTemplate: (data: string) => Template
}

export const useTemplateStore = create<TemplateState>()(
  devtools(
    persist(
      (set, get) => ({
        templates: [
          {
            id: '1',
            name: 'React + TypeScript Web App',
            description: 'Complete CI/CD pipeline for React applications with TypeScript, testing, and deployment to AWS S3.',
            category: 'web',
            tags: ['react', 'typescript', 'aws', 's3', 'jest'],
            version: '1.0.0',
            pipeline: {
              name: 'React TypeScript Pipeline',
              description: 'Automated build and deployment for React TypeScript applications',
              version: '1.0.0',
              nodes: [
                {
                  id: 'source',
                  type: 'git',
                  label: 'Source Code',
                  position: { x: 100, y: 100 },
                  data: {
                    config: {
                      repository: '{{REPOSITORY_URL}}',
                      branch: '{{BRANCH}}'
                    }
                  }
                },
                {
                  id: 'install',
                  type: 'script',
                  label: 'Install Dependencies',
                  position: { x: 100, y: 200 },
                  data: {
                    config: {
                      script: 'npm ci',
                      interpreter: 'bash'
                    }
                  }
                },
                {
                  id: 'test',
                  type: 'test',
                  label: 'Run Tests',
                  position: { x: 100, y: 300 },
                  data: {
                    config: {
                      command: 'npm test -- --coverage --watchAll=false'
                    }
                  }
                },
                {
                  id: 'build',
                  type: 'build',
                  label: 'Build Application',
                  position: { x: 100, y: 400 },
                  data: {
                    config: {
                      command: 'npm run build'
                    }
                  }
                },
                {
                  id: 'deploy',
                  type: 'deploy-cloud',
                  label: 'Deploy to S3',
                  position: { x: 100, y: 500 },
                  data: {
                    config: {
                      provider: 'aws',
                      service: 's3',
                      bucket: '{{S3_BUCKET}}',
                      region: '{{AWS_REGION}}'
                    }
                  }
                }
              ],
              edges: [
                { id: 'e1', source: 'source', target: 'install' },
                { id: 'e2', source: 'install', target: 'test' },
                { id: 'e3', source: 'test', target: 'build' },
                { id: 'e4', source: 'build', target: 'deploy' }
              ],
              variables: {},
              secrets: ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY'],
              triggers: [
                {
                  id: 'git-trigger',
                  type: 'git_push',
                  config: {
                    git: {
                      repository: '{{REPOSITORY_URL}}',
                      branch: '{{BRANCH}}',
                      events: ['push']
                    }
                  },
                  enabled: true
                }
              ],
              status: 'draft',
              tags: ['react', 'typescript', 'web'],
              metadata: {
                complexity: 'moderate',
                category: 'web'
              }
            },
            parameters: [
              {
                name: 'REPOSITORY_URL',
                type: 'string',
                description: 'Git repository URL',
                required: true
              },
              {
                name: 'BRANCH',
                type: 'string',
                description: 'Git branch to build from',
                required: false,
                defaultValue: 'main'
              },
              {
                name: 'S3_BUCKET',
                type: 'string',
                description: 'S3 bucket name for deployment',
                required: true
              },
              {
                name: 'AWS_REGION',
                type: 'select',
                description: 'AWS region',
                required: true,
                defaultValue: 'us-east-1',
                options: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']
              }
            ],
            author: 'Pipeline Team',
            isPublic: true,
            downloads: 1250,
            rating: 4.8,
            reviews: [
              {
                id: '1',
                userId: 'user1',
                rating: 5,
                comment: 'Excellent template, saved me hours of setup!',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7)
              },
              {
                id: '2',
                userId: 'user2',
                rating: 4,
                comment: 'Good template, but could use more documentation.',
                createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3)
              }
            ],
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2)
          },
          {
            id: '2',
            name: 'Node.js API with Docker',
            description: 'Build, test, and deploy Node.js APIs using Docker containers to Kubernetes clusters.',
            category: 'api',
            tags: ['nodejs', 'docker', 'kubernetes', 'api', 'express'],
            version: '1.2.0',
            pipeline: {
              name: 'Node.js Docker API Pipeline',
              description: 'Containerized Node.js API deployment pipeline',
              version: '1.2.0',
              nodes: [
                {
                  id: 'source',
                  type: 'git',
                  label: 'Source Code',
                  position: { x: 100, y: 100 },
                  data: {
                    config: {
                      repository: '{{REPOSITORY_URL}}',
                      branch: '{{BRANCH}}'
                    }
                  }
                },
                {
                  id: 'test',
                  type: 'test',
                  label: 'Run Tests',
                  position: { x: 100, y: 200 },
                  data: {
                    config: {
                      command: 'npm test'
                    }
                  }
                },
                {
                  id: 'docker-build',
                  type: 'docker',
                  label: 'Build Docker Image',
                  position: { x: 100, y: 300 },
                  data: {
                    config: {
                      dockerfile: 'Dockerfile',
                      tag: '{{IMAGE_TAG}}'
                    }
                  }
                },
                {
                  id: 'deploy-k8s',
                  type: 'deploy-k8s',
                  label: 'Deploy to Kubernetes',
                  position: { x: 100, y: 400 },
                  data: {
                    config: {
                      cluster: '{{K8S_CLUSTER}}',
                      namespace: '{{K8S_NAMESPACE}}',
                      image: '{{IMAGE_TAG}}'
                    }
                  }
                }
              ],
              edges: [
                { id: 'e1', source: 'source', target: 'test' },
                { id: 'e2', source: 'test', target: 'docker-build' },
                { id: 'e3', source: 'docker-build', target: 'deploy-k8s' }
              ],
              variables: {},
              secrets: ['DOCKER_REGISTRY_TOKEN', 'K8S_CONFIG'],
              triggers: [],
              status: 'draft',
              tags: ['nodejs', 'docker', 'kubernetes'],
              metadata: {
                complexity: 'complex',
                category: 'api'
              }
            },
            parameters: [
              {
                name: 'REPOSITORY_URL',
                type: 'string',
                description: 'Git repository URL',
                required: true
              },
              {
                name: 'BRANCH',
                type: 'string',
                description: 'Git branch to build from',
                required: false,
                defaultValue: 'main'
              },
              {
                name: 'IMAGE_TAG',
                type: 'string',
                description: 'Docker image tag',
                required: true
              },
              {
                name: 'K8S_CLUSTER',
                type: 'string',
                description: 'Kubernetes cluster name',
                required: true
              },
              {
                name: 'K8S_NAMESPACE',
                type: 'string',
                description: 'Kubernetes namespace',
                required: false,
                defaultValue: 'default'
              }
            ],
            author: 'DevOps Community',
            isPublic: true,
            downloads: 890,
            rating: 4.6,
            reviews: [],
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45),
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5)
          }
        ],
        selectedTemplate: null,
        isLoading: false,

        createTemplate: (templateData) => {
          const template: Template = {
            ...templateData,
            id: generateId(),
            downloads: 0,
            rating: 0,
            reviews: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          set((state) => ({
            templates: [...state.templates, template]
          }))
          
          return template
        },

        updateTemplate: (id: string, updates: Partial<Template>) => {
          set((state) => ({
            templates: state.templates.map(template =>
              template.id === id 
                ? { ...template, ...updates, updatedAt: new Date() }
                : template
            ),
            selectedTemplate: state.selectedTemplate?.id === id
              ? { ...state.selectedTemplate, ...updates, updatedAt: new Date() }
              : state.selectedTemplate
          }))
        },

        deleteTemplate: (id: string) => {
          set((state) => ({
            templates: state.templates.filter(template => template.id !== id),
            selectedTemplate: state.selectedTemplate?.id === id ? null : state.selectedTemplate
          }))
        },

        selectTemplate: (template: Template | null) => {
          set({ selectedTemplate: template })
        },

        instantiateTemplate: (templateId: string, parameters: Record<string, any>) => {
          const template = get().templates.find(t => t.id === templateId)
          if (!template) {
            throw new Error('Template not found')
          }

          // Clone the pipeline from template
          const pipeline: Pipeline = {
            ...template.pipeline,
            id: generateId(),
            name: `${template.pipeline.name} (from template)`,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'current-user' // TODO: Get from auth
          }

          // Replace parameter placeholders
          const replaceParameters = (obj: any): any => {
            if (typeof obj === 'string') {
              let result = obj
              Object.entries(parameters).forEach(([key, value]) => {
                result = result.replace(new RegExp(`{{${key}}}`, 'g'), String(value))
              })
              return result
            } else if (Array.isArray(obj)) {
              return obj.map(replaceParameters)
            } else if (obj && typeof obj === 'object') {
              const newObj: any = {}
              Object.entries(obj).forEach(([key, value]) => {
                newObj[key] = replaceParameters(value)
              })
              return newObj
            }
            return obj
          }

          pipeline.nodes = replaceParameters(pipeline.nodes)
          pipeline.triggers = replaceParameters(pipeline.triggers)

          // Increment download count
          get().updateTemplate(templateId, { 
            downloads: template.downloads + 1 
          })

          return pipeline
        },

        publishTemplate: async (templateId: string) => {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          get().updateTemplate(templateId, { isPublic: true })
        },

        unpublishTemplate: async (templateId: string) => {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          get().updateTemplate(templateId, { isPublic: false })
        },

        addReview: (templateId: string, reviewData: Omit<TemplateReview, 'id' | 'createdAt'>) => {
          const template = get().templates.find(t => t.id === templateId)
          if (!template) return

          const review: TemplateReview = {
            ...reviewData,
            id: generateId(),
            createdAt: new Date()
          }

          const updatedReviews = [...template.reviews, review]
          
          get().updateTemplate(templateId, { reviews: updatedReviews })
          get().updateRating(templateId)
        },

        updateRating: (templateId: string) => {
          const template = get().templates.find(t => t.id === templateId)
          if (!template || template.reviews.length === 0) return

          const averageRating = template.reviews.reduce((sum, review) => sum + review.rating, 0) / template.reviews.length
          
          get().updateTemplate(templateId, { rating: Math.round(averageRating * 10) / 10 })
        },

        searchTemplates: (query: string) => {
          const lowercaseQuery = query.toLowerCase()
          return get().templates.filter(template =>
            template.name.toLowerCase().includes(lowercaseQuery) ||
            template.description.toLowerCase().includes(lowercaseQuery) ||
            template.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
            template.category.toLowerCase().includes(lowercaseQuery)
          )
        },

        getTemplatesByCategory: (category: string) => {
          return get().templates.filter(template => template.category === category)
        },

        getTemplatesByAuthor: (author: string) => {
          return get().templates.filter(template => template.author === author)
        },

        getPopularTemplates: (limit = 10) => {
          return get().templates
            .sort((a, b) => b.downloads - a.downloads)
            .slice(0, limit)
        },

        exportTemplate: (templateId: string) => {
          const template = get().templates.find(t => t.id === templateId)
          if (!template) {
            throw new Error('Template not found')
          }
          return JSON.stringify(template, null, 2)
        },

        importTemplate: (data: string) => {
          try {
            const templateData = JSON.parse(data)
            const template = get().createTemplate({
              ...templateData,
              author: 'current-user', // TODO: Get from auth
              isPublic: false
            })
            return template
          } catch (error) {
            throw new Error('Invalid template data')
          }
        }
      }),
      {
        name: 'template-store',
        partialize: (state) => ({
          templates: state.templates
        })
      }
    ),
    { name: 'template-store' }
  )
)
