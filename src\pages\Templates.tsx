import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Plus, 
  FileTemplate, 
  Star, 
  Download, 
  Eye, 
  Search,
  Filter,
  GitBranch,
  Package,
  Server,
  Database,
  Globe,
  Smartphone
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

interface Template {
  id: string
  name: string
  description: string
  category: 'web' | 'mobile' | 'api' | 'database' | 'devops' | 'ml'
  tags: string[]
  author: string
  downloads: number
  rating: number
  lastUpdated: Date
  isPublic: boolean
  isFavorite: boolean
  complexity: 'beginner' | 'intermediate' | 'advanced'
}

export function Templates() {
  const { toast } = useToast()
  const [templates, setTemplates] = useState<Template[]>([
    {
      id: '1',
      name: 'React + TypeScript Web App',
      description: 'Complete CI/CD pipeline for React applications with TypeScript, testing, and deployment to AWS S3.',
      category: 'web',
      tags: ['react', 'typescript', 'aws', 's3', 'jest'],
      author: 'Pipeline Team',
      downloads: 1250,
      rating: 4.8,
      lastUpdated: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
      isPublic: true,
      isFavorite: true,
      complexity: 'intermediate'
    },
    {
      id: '2',
      name: 'Node.js API with Docker',
      description: 'Build, test, and deploy Node.js APIs using Docker containers to Kubernetes clusters.',
      category: 'api',
      tags: ['nodejs', 'docker', 'kubernetes', 'api', 'express'],
      author: 'DevOps Community',
      downloads: 890,
      rating: 4.6,
      lastUpdated: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5),
      isPublic: true,
      isFavorite: false,
      complexity: 'advanced'
    },
    {
      id: '3',
      name: 'Database Migration Pipeline',
      description: 'Automated database schema migrations with rollback support for PostgreSQL and MySQL.',
      category: 'database',
      tags: ['postgresql', 'mysql', 'migration', 'rollback'],
      author: 'Database Team',
      downloads: 456,
      rating: 4.4,
      lastUpdated: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
      isPublic: true,
      isFavorite: false,
      complexity: 'intermediate'
    },
    {
      id: '4',
      name: 'Mobile App CI/CD',
      description: 'Build and deploy React Native apps to App Store and Google Play Store.',
      category: 'mobile',
      tags: ['react-native', 'ios', 'android', 'app-store', 'play-store'],
      author: 'Mobile Team',
      downloads: 678,
      rating: 4.7,
      lastUpdated: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
      isPublic: true,
      isFavorite: true,
      complexity: 'advanced'
    },
    {
      id: '5',
      name: 'Infrastructure as Code',
      description: 'Deploy cloud infrastructure using Terraform with automated testing and validation.',
      category: 'devops',
      tags: ['terraform', 'aws', 'infrastructure', 'iac'],
      author: 'Infrastructure Team',
      downloads: 234,
      rating: 4.5,
      lastUpdated: new Date(Date.now() - 1000 * 60 * 60 * 24),
      isPublic: true,
      isFavorite: false,
      complexity: 'advanced'
    }
  ])

  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const [complexityFilter, setComplexityFilter] = useState<string>('all')
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false)

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter
    const matchesComplexity = complexityFilter === 'all' || template.complexity === complexityFilter
    const matchesFavorites = !showFavoritesOnly || template.isFavorite
    
    return matchesSearch && matchesCategory && matchesComplexity && matchesFavorites
  })

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'web':
        return <Globe className="h-5 w-5" />
      case 'mobile':
        return <Smartphone className="h-5 w-5" />
      case 'api':
        return <Server className="h-5 w-5" />
      case 'database':
        return <Database className="h-5 w-5" />
      case 'devops':
        return <Package className="h-5 w-5" />
      case 'ml':
        return <GitBranch className="h-5 w-5" />
      default:
        return <FileTemplate className="h-5 w-5" />
    }
  }

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const toggleFavorite = (templateId: string) => {
    setTemplates(templates.map(template =>
      template.id === templateId
        ? { ...template, isFavorite: !template.isFavorite }
        : template
    ))
  }

  const useTemplate = (templateId: string) => {
    toast({
      title: "Template Applied",
      description: "Creating new pipeline from template...",
    })
    // Navigate to designer with template
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Pipeline Templates</h1>
          <p className="text-muted-foreground">
            Start with pre-built templates or create your own reusable pipelines.
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Templates</p>
              <p className="text-2xl font-bold">{templates.length}</p>
            </div>
            <FileTemplate className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Favorites</p>
              <p className="text-2xl font-bold text-yellow-500">
                {templates.filter(t => t.isFavorite).length}
              </p>
            </div>
            <Star className="h-8 w-8 text-yellow-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Downloads</p>
              <p className="text-2xl font-bold">
                {templates.reduce((sum, t) => sum + t.downloads, 0).toLocaleString()}
              </p>
            </div>
            <Download className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Avg Rating</p>
              <p className="text-2xl font-bold">
                {(templates.reduce((sum, t) => sum + t.rating, 0) / templates.length).toFixed(1)}
              </p>
            </div>
            <Star className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          />
        </div>

        <select
          value={categoryFilter}
          onChange={(e) => setCategoryFilter(e.target.value)}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <option value="all">All Categories</option>
          <option value="web">Web</option>
          <option value="mobile">Mobile</option>
          <option value="api">API</option>
          <option value="database">Database</option>
          <option value="devops">DevOps</option>
          <option value="ml">Machine Learning</option>
        </select>

        <select
          value={complexityFilter}
          onChange={(e) => setComplexityFilter(e.target.value)}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm focus:border-ring focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        >
          <option value="all">All Levels</option>
          <option value="beginner">Beginner</option>
          <option value="intermediate">Intermediate</option>
          <option value="advanced">Advanced</option>
        </select>

        <Button
          variant={showFavoritesOnly ? "default" : "outline"}
          onClick={() => setShowFavoritesOnly(!showFavoritesOnly)}
        >
          <Star className="mr-2 h-4 w-4" />
          Favorites
        </Button>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <div key={template.id} className="rounded-lg border bg-card p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                {getCategoryIcon(template.category)}
                <span className="text-sm text-muted-foreground capitalize">{template.category}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleFavorite(template.id)}
              >
                <Star className={`h-4 w-4 ${template.isFavorite ? 'fill-yellow-400 text-yellow-400' : ''}`} />
              </Button>
            </div>

            <h3 className="text-lg font-semibold mb-2">{template.name}</h3>
            <p className="text-sm text-muted-foreground mb-4">{template.description}</p>

            <div className="flex flex-wrap gap-1 mb-4">
              {template.tags.slice(0, 3).map((tag) => (
                <span key={tag} className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
                  {tag}
                </span>
              ))}
              {template.tags.length > 3 && (
                <span className="px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-xs">
                  +{template.tags.length - 3}
                </span>
              )}
            </div>

            <div className="flex items-center justify-between mb-4">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getComplexityColor(template.complexity)}`}>
                {template.complexity}
              </span>
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm">{template.rating}</span>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
              <span>{template.downloads} downloads</span>
              <span>Updated {formatDate(template.lastUpdated)}</span>
            </div>

            <div className="flex space-x-2">
              <Button onClick={() => useTemplate(template.id)} className="flex-1">
                Use Template
              </Button>
              <Button variant="outline" size="icon">
                <Eye className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
