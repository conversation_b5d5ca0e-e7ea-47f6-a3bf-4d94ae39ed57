import { useState } from 'react'
import { 
  Save, 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Database, 
  Cloud,
  Key,
  Mail,
  Smartphone,
  Globe,
  Settings as SettingsIcon
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTheme } from '@/components/theme-provider'
import { useToast } from '@/hooks/use-toast'

interface UserSettings {
  profile: {
    name: string
    email: string
    avatar?: string
    timezone: string
    language: string
  }
  notifications: {
    email: boolean
    slack: boolean
    webhook: boolean
    pipelineSuccess: boolean
    pipelineFailure: boolean
    deploymentComplete: boolean
    securityAlerts: boolean
  }
  security: {
    twoFactorEnabled: boolean
    sessionTimeout: number
    allowedIPs: string[]
    apiKeyRotation: number
  }
  preferences: {
    defaultView: string
    autoSave: boolean
    showTutorials: boolean
    compactMode: boolean
  }
  integrations: {
    github: boolean
    gitlab: boolean
    aws: boolean
    azure: boolean
    gcp: boolean
    docker: boolean
    kubernetes: boolean
  }
}

export function Settings() {
  const { theme, setTheme } = useTheme()
  const { toast } = useToast()
  
  const [settings, setSettings] = useState<UserSettings>({
    profile: {
      name: '<PERSON>',
      email: '<EMAIL>',
      timezone: 'UTC-5',
      language: 'en'
    },
    notifications: {
      email: true,
      slack: true,
      webhook: false,
      pipelineSuccess: true,
      pipelineFailure: true,
      deploymentComplete: true,
      securityAlerts: true
    },
    security: {
      twoFactorEnabled: true,
      sessionTimeout: 480, // 8 hours
      allowedIPs: ['***********/24'],
      apiKeyRotation: 90 // days
    },
    preferences: {
      defaultView: 'dashboard',
      autoSave: true,
      showTutorials: true,
      compactMode: false
    },
    integrations: {
      github: true,
      gitlab: false,
      aws: true,
      azure: false,
      gcp: false,
      docker: true,
      kubernetes: true
    }
  })

  const [activeTab, setActiveTab] = useState('profile')

  const handleSave = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "Settings Saved",
        description: "Your settings have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      })
    }
  }

  const updateSettings = (section: keyof UserSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }))
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'integrations', label: 'Integrations', icon: Cloud },
    { id: 'preferences', label: 'Preferences', icon: SettingsIcon },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>
        <Button onClick={handleSave}>
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      <div className="flex space-x-6">
        {/* Sidebar */}
        <div className="w-64 space-y-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`w-full flex items-center space-x-3 px-3 py-2 text-left rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 rounded-lg border bg-card p-6">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Profile Settings</h2>
              
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium">Full Name</label>
                  <input
                    type="text"
                    value={settings.profile.name}
                    onChange={(e) => updateSettings('profile', 'name', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <input
                    type="email"
                    value={settings.profile.email}
                    onChange={(e) => updateSettings('profile', 'email', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Timezone</label>
                  <select
                    value={settings.profile.timezone}
                    onChange={(e) => updateSettings('profile', 'timezone', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  >
                    <option value="UTC-8">Pacific Time (UTC-8)</option>
                    <option value="UTC-5">Eastern Time (UTC-5)</option>
                    <option value="UTC+0">UTC</option>
                    <option value="UTC+1">Central European Time (UTC+1)</option>
                  </select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Language</label>
                  <select
                    value={settings.profile.language}
                    onChange={(e) => updateSettings('profile', 'language', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  >
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Notification Settings</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-3">Notification Channels</h3>
                  <div className="space-y-3">
                    {Object.entries(settings.notifications).slice(0, 3).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {key === 'email' && <Mail className="h-4 w-4" />}
                          {key === 'slack' && <Smartphone className="h-4 w-4" />}
                          {key === 'webhook' && <Globe className="h-4 w-4" />}
                          <span className="capitalize">{key}</span>
                        </div>
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => updateSettings('notifications', key, e.target.checked)}
                          className="rounded"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Event Notifications</h3>
                  <div className="space-y-3">
                    {Object.entries(settings.notifications).slice(3).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => updateSettings('notifications', key, e.target.checked)}
                          className="rounded"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Security Settings</h2>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Two-Factor Authentication</h3>
                    <p className="text-sm text-muted-foreground">Add an extra layer of security</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.security.twoFactorEnabled}
                    onChange={(e) => updateSettings('security', 'twoFactorEnabled', e.target.checked)}
                    className="rounded"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">API Key Rotation (days)</label>
                  <input
                    type="number"
                    value={settings.security.apiKeyRotation}
                    onChange={(e) => updateSettings('security', 'apiKeyRotation', parseInt(e.target.value))}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'appearance' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Appearance Settings</h2>
              
              <div>
                <h3 className="font-medium mb-3">Theme</h3>
                <div className="flex space-x-2">
                  <Button
                    variant={theme === 'light' ? 'default' : 'outline'}
                    onClick={() => setTheme('light')}
                  >
                    Light
                  </Button>
                  <Button
                    variant={theme === 'dark' ? 'default' : 'outline'}
                    onClick={() => setTheme('dark')}
                  >
                    Dark
                  </Button>
                  <Button
                    variant={theme === 'system' ? 'default' : 'outline'}
                    onClick={() => setTheme('system')}
                  >
                    System
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Compact Mode</h3>
                  <p className="text-sm text-muted-foreground">Reduce spacing and padding</p>
                </div>
                <input
                  type="checkbox"
                  checked={settings.preferences.compactMode}
                  onChange={(e) => updateSettings('preferences', 'compactMode', e.target.checked)}
                  className="rounded"
                />
              </div>
            </div>
          )}

          {activeTab === 'integrations' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Integration Settings</h2>
              
              <div className="grid gap-4 md:grid-cols-2">
                {Object.entries(settings.integrations).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-3 border border-border rounded-md">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
                        <span className="text-xs font-medium uppercase">{key.slice(0, 2)}</span>
                      </div>
                      <span className="capitalize">{key}</span>
                    </div>
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => updateSettings('integrations', key, e.target.checked)}
                      className="rounded"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Preferences</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Default View</label>
                  <select
                    value={settings.preferences.defaultView}
                    onChange={(e) => updateSettings('preferences', 'defaultView', e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  >
                    <option value="dashboard">Dashboard</option>
                    <option value="designer">Pipeline Designer</option>
                    <option value="deployments">Deployment Targets</option>
                  </select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Auto Save</h3>
                    <p className="text-sm text-muted-foreground">Automatically save changes</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.preferences.autoSave}
                    onChange={(e) => updateSettings('preferences', 'autoSave', e.target.checked)}
                    className="rounded"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Show Tutorials</h3>
                    <p className="text-sm text-muted-foreground">Display helpful tutorials and tips</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.preferences.showTutorials}
                    onChange={(e) => updateSettings('preferences', 'showTutorials', e.target.checked)}
                    className="rounded"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
