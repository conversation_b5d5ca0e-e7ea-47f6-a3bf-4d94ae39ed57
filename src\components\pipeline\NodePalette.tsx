import { 
  GitBranch, 
  Package, 
  TestTube, 
  Server, 
  Database, 
  Mail, 
  Webhook,
  FileText,
  Shield,
  Zap,
  Docker,
  Cloud
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface NodePaletteProps {
  onAddNode: (nodeType: string) => void
}

const nodeCategories = [
  {
    title: 'Source',
    nodes: [
      { type: 'git', label: 'Git Repository', icon: GitBranch, description: 'Pull code from Git repository' },
      { type: 'webhook', label: 'Webhook Trigger', icon: Webhook, description: 'Trigger pipeline via webhook' },
      { type: 'schedule', label: 'Scheduled Trigger', icon: Zap, description: 'Run on schedule' },
    ]
  },
  {
    title: 'Build & Test',
    nodes: [
      { type: 'build', label: 'Build', icon: Package, description: 'Build application' },
      { type: 'test', label: 'Test', icon: TestTube, description: 'Run tests' },
      { type: 'docker', label: 'Docker Build', icon: Docker, description: 'Build Docker image' },
      { type: 'security', label: 'Security Scan', icon: Shield, description: 'Security vulnerability scan' },
    ]
  },
  {
    title: 'Deploy',
    nodes: [
      { type: 'deploy-server', label: 'Server Deploy', icon: Server, description: 'Deploy to server' },
      { type: 'deploy-cloud', label: 'Cloud Deploy', icon: Cloud, description: 'Deploy to cloud platform' },
      { type: 'deploy-k8s', label: 'Kubernetes', icon: Server, description: 'Deploy to Kubernetes' },
      { type: 'database', label: 'Database Migration', icon: Database, description: 'Run database migrations' },
    ]
  },
  {
    title: 'Notify',
    nodes: [
      { type: 'email', label: 'Email', icon: Mail, description: 'Send email notification' },
      { type: 'slack', label: 'Slack', icon: Mail, description: 'Send Slack message' },
      { type: 'webhook-notify', label: 'Webhook', icon: Webhook, description: 'Send webhook notification' },
    ]
  },
  {
    title: 'Utilities',
    nodes: [
      { type: 'script', label: 'Custom Script', icon: FileText, description: 'Run custom script' },
      { type: 'condition', label: 'Condition', icon: Zap, description: 'Conditional execution' },
      { type: 'parallel', label: 'Parallel', icon: Zap, description: 'Run steps in parallel' },
    ]
  }
]

export function NodePalette({ onAddNode }: NodePaletteProps) {
  return (
    <div className="p-4 h-full overflow-y-auto">
      <h2 className="text-lg font-semibold mb-4">Node Palette</h2>
      
      <div className="space-y-6">
        {nodeCategories.map((category) => (
          <div key={category.title}>
            <h3 className="text-sm font-medium text-muted-foreground mb-2 uppercase tracking-wide">
              {category.title}
            </h3>
            <div className="space-y-1">
              {category.nodes.map((node) => (
                <button
                  key={node.type}
                  onClick={() => onAddNode(node.type)}
                  className="w-full p-3 text-left rounded-md border border-border hover:bg-accent hover:border-accent-foreground transition-colors group"
                >
                  <div className="flex items-start space-x-3">
                    <node.icon className="h-5 w-5 text-muted-foreground group-hover:text-accent-foreground mt-0.5" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium group-hover:text-accent-foreground">
                        {node.label}
                      </p>
                      <p className="text-xs text-muted-foreground group-hover:text-accent-foreground/80">
                        {node.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
