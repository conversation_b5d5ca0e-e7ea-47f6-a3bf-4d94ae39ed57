import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { DeploymentTarget } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

interface DeploymentState {
  targets: DeploymentTarget[]
  selectedTarget: DeploymentTarget | null
  isConnecting: boolean
  connectionResults: Record<string, { success: boolean; message: string; timestamp: Date }>
  
  // Actions
  addTarget: (target: Omit<DeploymentTarget, 'id' | 'createdAt' | 'updatedAt' | 'deploymentCount'>) => DeploymentTarget
  updateTarget: (id: string, updates: Partial<DeploymentTarget>) => void
  deleteTarget: (id: string) => void
  selectTarget: (target: DeploymentTarget | null) => void
  
  // Connection testing
  testConnection: (targetId: string) => Promise<{ success: boolean; message: string }>
  updateTargetStatus: (targetId: string, status: DeploymentTarget['status']) => void
  
  // Deployment operations
  deploy: (targetId: string, artifacts: string[], config?: Record<string, any>) => Promise<void>
  getDeploymentHistory: (targetId: string) => any[]
  
  // Health checks
  performHealthCheck: (targetId: string) => Promise<boolean>
  scheduleHealthChecks: () => void
}

export const useDeploymentStore = create<DeploymentState>()(
  devtools(
    persist(
      (set, get) => ({
        targets: [
          {
            id: '1',
            name: 'Production Server',
            type: 'server',
            status: 'connected',
            config: {
              endpoint: '192.168.1.100',
              auth: {
                type: 'ssh',
                credentials: {
                  username: 'deploy',
                  keyPath: '/path/to/key'
                }
              },
              environment: 'production'
            },
            healthCheck: {
              url: 'http://192.168.1.100/health',
              interval: 300, // 5 minutes
              timeout: 30,
              retries: 3
            },
            lastDeployment: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            deploymentCount: 45,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24) // 1 day ago
          },
          {
            id: '2',
            name: 'AWS ECS Cluster',
            type: 'cloud',
            status: 'connected',
            config: {
              endpoint: 'ecs.us-east-1.amazonaws.com',
              auth: {
                type: 'token',
                credentials: {
                  accessKeyId: 'AKIA...',
                  secretAccessKey: '***',
                  region: 'us-east-1'
                }
              },
              environment: 'production',
              region: 'us-east-1'
            },
            lastDeployment: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            deploymentCount: 128,
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60), // 60 days ago
            updatedAt: new Date(Date.now() - 1000 * 60 * 60) // 1 hour ago
          }
        ],
        selectedTarget: null,
        isConnecting: false,
        connectionResults: {},

        addTarget: (targetData) => {
          const target: DeploymentTarget = {
            ...targetData,
            id: generateId(),
            deploymentCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          set((state) => ({
            targets: [...state.targets, target]
          }))
          
          return target
        },

        updateTarget: (id: string, updates: Partial<DeploymentTarget>) => {
          set((state) => ({
            targets: state.targets.map(target =>
              target.id === id 
                ? { ...target, ...updates, updatedAt: new Date() }
                : target
            ),
            selectedTarget: state.selectedTarget?.id === id
              ? { ...state.selectedTarget, ...updates, updatedAt: new Date() }
              : state.selectedTarget
          }))
        },

        deleteTarget: (id: string) => {
          set((state) => ({
            targets: state.targets.filter(target => target.id !== id),
            selectedTarget: state.selectedTarget?.id === id ? null : state.selectedTarget
          }))
        },

        selectTarget: (target: DeploymentTarget | null) => {
          set({ selectedTarget: target })
        },

        testConnection: async (targetId: string) => {
          const target = get().targets.find(t => t.id === targetId)
          if (!target) {
            throw new Error('Target not found')
          }

          set({ isConnecting: true })

          try {
            // Simulate connection test
            await new Promise(resolve => setTimeout(resolve, 2000))
            
            // Mock connection result based on target type
            const success = Math.random() > 0.2 // 80% success rate
            const message = success 
              ? `Successfully connected to ${target.name}`
              : `Failed to connect to ${target.name}: Connection timeout`

            const result = { success, message, timestamp: new Date() }
            
            set((state) => ({
              connectionResults: {
                ...state.connectionResults,
                [targetId]: result
              },
              isConnecting: false
            }))

            // Update target status based on connection result
            get().updateTargetStatus(targetId, success ? 'connected' : 'error')

            return { success, message }
          } catch (error) {
            const result = { 
              success: false, 
              message: `Connection failed: ${error}`,
              timestamp: new Date()
            }
            
            set((state) => ({
              connectionResults: {
                ...state.connectionResults,
                [targetId]: result
              },
              isConnecting: false
            }))

            get().updateTargetStatus(targetId, 'error')
            throw error
          }
        },

        updateTargetStatus: (targetId: string, status: DeploymentTarget['status']) => {
          get().updateTarget(targetId, { status })
        },

        deploy: async (targetId: string, artifacts: string[], config = {}) => {
          const target = get().targets.find(t => t.id === targetId)
          if (!target) {
            throw new Error('Target not found')
          }

          // Simulate deployment
          await new Promise(resolve => setTimeout(resolve, 3000))

          // Update deployment count and last deployment time
          get().updateTarget(targetId, {
            deploymentCount: target.deploymentCount + 1,
            lastDeployment: new Date()
          })
        },

        getDeploymentHistory: (targetId: string) => {
          // Mock deployment history
          return [
            {
              id: '1',
              timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
              status: 'success',
              artifacts: ['app-v1.2.3.tar.gz'],
              duration: 120000
            },
            {
              id: '2',
              timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
              status: 'success',
              artifacts: ['app-v1.2.2.tar.gz'],
              duration: 95000
            }
          ]
        },

        performHealthCheck: async (targetId: string) => {
          const target = get().targets.find(t => t.id === targetId)
          if (!target || !target.healthCheck) {
            return false
          }

          try {
            // Simulate health check
            await new Promise(resolve => setTimeout(resolve, 1000))
            
            const isHealthy = Math.random() > 0.1 // 90% healthy
            
            get().updateTargetStatus(targetId, isHealthy ? 'connected' : 'error')
            
            return isHealthy
          } catch (error) {
            get().updateTargetStatus(targetId, 'error')
            return false
          }
        },

        scheduleHealthChecks: () => {
          const targets = get().targets.filter(t => t.healthCheck)
          
          targets.forEach(target => {
            if (target.healthCheck) {
              setInterval(() => {
                get().performHealthCheck(target.id)
              }, target.healthCheck.interval * 1000)
            }
          })
        }
      }),
      {
        name: 'deployment-store',
        partialize: (state) => ({
          targets: state.targets
        })
      }
    ),
    { name: 'deployment-store' }
  )
)
