import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { Secret, SecretAccess } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

interface SecretsState {
  secrets: Secret[]
  selectedSecret: Secret | null
  isLoading: boolean
  
  // Actions
  addSecret: (secret: Omit<Secret, 'id' | 'createdAt' | 'updatedAt' | 'accessLog' | 'isExpired'>) => Secret
  updateSecret: (id: string, updates: Partial<Secret>) => void
  deleteSecret: (id: string) => void
  selectSecret: (secret: Secret | null) => void
  
  // Secret operations
  getSecretValue: (id: string) => Promise<string>
  rotateSecret: (id: string, newValue: string) => Promise<void>
  
  // Access logging
  logAccess: (secretId: string, action: SecretAccess['action'], source: string) => void
  getAccessHistory: (secretId: string) => SecretAccess[]
  
  // Expiry management
  checkExpiredSecrets: () => Secret[]
  getExpiringSecrets: (days: number) => Secret[]
  
  // Scope filtering
  getSecretsByScope: (scope: Secret['scope'], scopeValue?: string) => Secret[]
  
  // Encryption/Decryption (mock)
  encryptValue: (value: string) => string
  decryptValue: (encryptedValue: string) => string
}

export const useSecretsStore = create<SecretsState>()(
  devtools(
    persist(
      (set, get) => ({
        secrets: [
          {
            id: '1',
            name: 'DATABASE_PASSWORD',
            description: 'Production database password',
            scope: 'environment',
            scopeValue: 'production',
            type: 'password',
            value: 'encrypted_password_value_123', // This would be encrypted
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 30), // 30 days from now
            isExpired: false,
            rotationPolicy: {
              enabled: true,
              interval: 90,
              notifyBefore: 7
            },
            accessLog: [
              {
                id: '1',
                timestamp: new Date(Date.now() - 1000 * 60 * 60),
                user: 'pipeline-engine',
                action: 'read',
                source: 'pipeline',
                metadata: { pipelineId: 'pipeline-123' }
              }
            ],
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            createdBy: '<EMAIL>'
          },
          {
            id: '2',
            name: 'AWS_ACCESS_KEY',
            description: 'AWS access key for deployment',
            scope: 'global',
            type: 'token',
            value: 'encrypted_aws_key_456',
            expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24 * 90), // 90 days from now
            isExpired: false,
            accessLog: [],
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14), // 14 days ago
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
            createdBy: '<EMAIL>'
          },
          {
            id: '3',
            name: 'SSL_CERTIFICATE',
            description: 'SSL certificate for HTTPS',
            scope: 'project',
            scopeValue: 'web-app',
            type: 'certificate',
            value: 'encrypted_cert_789',
            expiresAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5), // Expired 5 days ago
            isExpired: true,
            accessLog: [],
            createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 365), // 1 year ago
            updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
            createdBy: '<EMAIL>'
          }
        ],
        selectedSecret: null,
        isLoading: false,

        addSecret: (secretData) => {
          const secret: Secret = {
            ...secretData,
            id: generateId(),
            value: get().encryptValue(secretData.value),
            isExpired: secretData.expiresAt ? secretData.expiresAt < new Date() : false,
            accessLog: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          set((state) => ({
            secrets: [...state.secrets, secret]
          }))
          
          // Log the creation
          get().logAccess(secret.id, 'write', 'ui')
          
          return secret
        },

        updateSecret: (id: string, updates: Partial<Secret>) => {
          set((state) => {
            const updatedSecrets = state.secrets.map(secret => {
              if (secret.id === id) {
                const updatedSecret = { 
                  ...secret, 
                  ...updates, 
                  updatedAt: new Date()
                }
                
                // Update expiry status if expiresAt changed
                if (updates.expiresAt) {
                  updatedSecret.isExpired = updates.expiresAt < new Date()
                }
                
                // Encrypt value if it's being updated
                if (updates.value) {
                  updatedSecret.value = get().encryptValue(updates.value)
                }
                
                return updatedSecret
              }
              return secret
            })
            
            return {
              secrets: updatedSecrets,
              selectedSecret: state.selectedSecret?.id === id
                ? updatedSecrets.find(s => s.id === id) || null
                : state.selectedSecret
            }
          })
          
          // Log the update
          get().logAccess(id, 'write', 'ui')
        },

        deleteSecret: (id: string) => {
          set((state) => ({
            secrets: state.secrets.filter(secret => secret.id !== id),
            selectedSecret: state.selectedSecret?.id === id ? null : state.selectedSecret
          }))
        },

        selectSecret: (secret: Secret | null) => {
          set({ selectedSecret: secret })
        },

        getSecretValue: async (id: string) => {
          const secret = get().secrets.find(s => s.id === id)
          if (!secret) {
            throw new Error('Secret not found')
          }
          
          if (secret.isExpired) {
            throw new Error('Secret has expired')
          }
          
          // Log the access
          get().logAccess(id, 'read', 'api')
          
          // Decrypt and return the value
          return get().decryptValue(secret.value)
        },

        rotateSecret: async (id: string, newValue: string) => {
          const secret = get().secrets.find(s => s.id === id)
          if (!secret) {
            throw new Error('Secret not found')
          }
          
          // Update the secret with new value and reset expiry
          const newExpiresAt = secret.rotationPolicy?.enabled
            ? new Date(Date.now() + secret.rotationPolicy.interval * 24 * 60 * 60 * 1000)
            : secret.expiresAt
          
          get().updateSecret(id, {
            value: newValue,
            expiresAt: newExpiresAt,
            isExpired: false
          })
          
          // Log the rotation
          get().logAccess(id, 'write', 'rotation')
        },

        logAccess: (secretId: string, action: SecretAccess['action'], source: string) => {
          const accessLog: SecretAccess = {
            id: generateId(),
            timestamp: new Date(),
            user: 'current-user', // TODO: Get from auth
            action,
            source,
            metadata: {}
          }
          
          set((state) => ({
            secrets: state.secrets.map(secret =>
              secret.id === secretId
                ? { ...secret, accessLog: [...secret.accessLog, accessLog] }
                : secret
            )
          }))
        },

        getAccessHistory: (secretId: string) => {
          const secret = get().secrets.find(s => s.id === secretId)
          return secret?.accessLog || []
        },

        checkExpiredSecrets: () => {
          const now = new Date()
          return get().secrets.filter(secret => 
            secret.expiresAt && secret.expiresAt < now
          )
        },

        getExpiringSecrets: (days: number) => {
          const futureDate = new Date(Date.now() + days * 24 * 60 * 60 * 1000)
          const now = new Date()
          
          return get().secrets.filter(secret =>
            secret.expiresAt && 
            secret.expiresAt > now && 
            secret.expiresAt <= futureDate
          )
        },

        getSecretsByScope: (scope: Secret['scope'], scopeValue?: string) => {
          return get().secrets.filter(secret => {
            if (secret.scope !== scope) return false
            if (scopeValue && secret.scopeValue !== scopeValue) return false
            return true
          })
        },

        encryptValue: (value: string) => {
          // Mock encryption - in real implementation, use proper encryption
          return btoa(value) + '_encrypted'
        },

        decryptValue: (encryptedValue: string) => {
          // Mock decryption - in real implementation, use proper decryption
          try {
            return atob(encryptedValue.replace('_encrypted', ''))
          } catch {
            return 'decryption_failed'
          }
        }
      }),
      {
        name: 'secrets-store',
        partialize: (state) => ({
          secrets: state.secrets.map(secret => ({
            ...secret,
            // Don't persist decrypted values
            value: secret.value
          }))
        })
      }
    ),
    { name: 'secrets-store' }
  )
)
