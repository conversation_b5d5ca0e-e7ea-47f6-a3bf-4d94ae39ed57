import { PipelineNode, PipelineExecution, NodeExecution } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

export interface Plugin {
  id: string
  name: string
  version: string
  description: string
  author: string
  category: 'node-executor' | 'ui-component' | 'validator' | 'analyzer' | 'integration'
  permissions: PluginPermission[]
  config?: PluginConfig
  manifest: PluginManifest
  code: string // JavaScript code
  isEnabled: boolean
  isVerified: boolean
  installDate: Date
  updateDate: Date
}

export interface PluginPermission {
  type: 'network' | 'filesystem' | 'secrets' | 'pipelines' | 'deployments' | 'system'
  scope: string
  description: string
}

export interface PluginConfig {
  schema: Record<string, any> // JSON schema for configuration
  defaults: Record<string, any>
  current: Record<string, any>
}

export interface PluginManifest {
  entryPoint: string
  dependencies?: string[]
  nodeTypes?: string[] // For node executor plugins
  hooks?: string[] // Lifecycle hooks the plugin subscribes to
  api?: {
    endpoints?: PluginEndpoint[]
    webhooks?: PluginWebhook[]
  }
}

export interface PluginEndpoint {
  path: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  handler: string // Function name in plugin code
  auth?: boolean
}

export interface PluginWebhook {
  event: string
  handler: string // Function name in plugin code
}

export interface PluginContext {
  plugin: Plugin
  config: Record<string, any>
  logger: PluginLogger
  api: PluginAPI
  storage: PluginStorage
}

export interface PluginLogger {
  debug(message: string, meta?: any): void
  info(message: string, meta?: any): void
  warn(message: string, meta?: any): void
  error(message: string, meta?: any): void
}

export interface PluginAPI {
  // Pipeline operations
  getPipeline(id: string): Promise<any>
  updatePipeline(id: string, updates: any): Promise<any>
  
  // Execution operations
  getExecution(id: string): Promise<any>
  logMessage(executionId: string, message: string, level?: string): Promise<void>
  
  // Secret operations (if permitted)
  getSecret(name: string): Promise<string>
  
  // HTTP requests (if permitted)
  fetch(url: string, options?: any): Promise<any>
}

export interface PluginStorage {
  get(key: string): Promise<any>
  set(key: string, value: any): Promise<void>
  delete(key: string): Promise<void>
  list(): Promise<string[]>
}

export class PluginSystem {
  private plugins = new Map<string, Plugin>()
  private pluginContexts = new Map<string, PluginContext>()
  private nodeExecutors = new Map<string, any>()
  private validators = new Map<string, any>()
  private analyzers = new Map<string, any>()
  private hooks = new Map<string, any[]>()

  async installPlugin(pluginData: Omit<Plugin, 'id' | 'installDate' | 'updateDate' | 'isEnabled'>): Promise<Plugin> {
    const plugin: Plugin = {
      ...pluginData,
      id: generateId(),
      installDate: new Date(),
      updateDate: new Date(),
      isEnabled: false
    }

    // Validate plugin
    await this.validatePlugin(plugin)

    // Create sandbox context
    const context = await this.createPluginContext(plugin)

    // Load plugin code
    await this.loadPlugin(plugin, context)

    this.plugins.set(plugin.id, plugin)
    this.pluginContexts.set(plugin.id, context)

    return plugin
  }

  async uninstallPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error('Plugin not found')
    }

    // Disable plugin first
    await this.disablePlugin(pluginId)

    // Clean up
    this.plugins.delete(pluginId)
    this.pluginContexts.delete(pluginId)

    // Remove registered components
    if (plugin.manifest.nodeTypes) {
      plugin.manifest.nodeTypes.forEach(type => {
        this.nodeExecutors.delete(type)
      })
    }
  }

  async enablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error('Plugin not found')
    }

    const context = this.pluginContexts.get(pluginId)
    if (!context) {
      throw new Error('Plugin context not found')
    }

    try {
      // Execute plugin initialization
      await this.executePluginFunction(plugin, context, 'onEnable')

      plugin.isEnabled = true
      plugin.updateDate = new Date()

      // Register plugin components
      await this.registerPluginComponents(plugin, context)

    } catch (error) {
      throw new Error(`Failed to enable plugin: ${error}`)
    }
  }

  async disablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId)
    if (!plugin) {
      throw new Error('Plugin not found')
    }

    const context = this.pluginContexts.get(pluginId)
    if (!context) {
      throw new Error('Plugin context not found')
    }

    try {
      // Execute plugin cleanup
      await this.executePluginFunction(plugin, context, 'onDisable')

      plugin.isEnabled = false
      plugin.updateDate = new Date()

      // Unregister plugin components
      this.unregisterPluginComponents(plugin)

    } catch (error) {
      console.error(`Error disabling plugin ${plugin.name}:`, error)
    }
  }

  async updatePlugin(pluginId: string, newVersion: Omit<Plugin, 'id' | 'installDate' | 'updateDate' | 'isEnabled'>): Promise<Plugin> {
    const existingPlugin = this.plugins.get(pluginId)
    if (!existingPlugin) {
      throw new Error('Plugin not found')
    }

    // Disable current version
    if (existingPlugin.isEnabled) {
      await this.disablePlugin(pluginId)
    }

    // Update plugin
    const updatedPlugin: Plugin = {
      ...newVersion,
      id: pluginId,
      installDate: existingPlugin.installDate,
      updateDate: new Date(),
      isEnabled: false
    }

    // Validate new version
    await this.validatePlugin(updatedPlugin)

    // Create new context
    const context = await this.createPluginContext(updatedPlugin)

    // Load new version
    await this.loadPlugin(updatedPlugin, context)

    this.plugins.set(pluginId, updatedPlugin)
    this.pluginContexts.set(pluginId, context)

    return updatedPlugin
  }

  getPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  getPlugin(id: string): Plugin | undefined {
    return this.plugins.get(id)
  }

  getEnabledPlugins(): Plugin[] {
    return this.getPlugins().filter(p => p.isEnabled)
  }

  // Node executor integration
  getNodeExecutor(nodeType: string): any {
    return this.nodeExecutors.get(nodeType)
  }

  // Hook system
  async executeHook(hookName: string, data: any): Promise<any[]> {
    const handlers = this.hooks.get(hookName) || []
    const results = []

    for (const handler of handlers) {
      try {
        const result = await handler(data)
        results.push(result)
      } catch (error) {
        console.error(`Hook handler error for ${hookName}:`, error)
      }
    }

    return results
  }

  private async validatePlugin(plugin: Plugin): Promise<void> {
    // Validate manifest
    if (!plugin.manifest.entryPoint) {
      throw new Error('Plugin manifest must specify an entry point')
    }

    // Validate permissions
    for (const permission of plugin.permissions) {
      if (!this.isPermissionAllowed(permission)) {
        throw new Error(`Permission not allowed: ${permission.type}:${permission.scope}`)
      }
    }

    // Validate code (basic syntax check)
    try {
      new Function(plugin.code)
    } catch (error) {
      throw new Error(`Plugin code syntax error: ${error}`)
    }
  }

  private async createPluginContext(plugin: Plugin): Promise<PluginContext> {
    const logger: PluginLogger = {
      debug: (message, meta) => console.debug(`[${plugin.name}] ${message}`, meta),
      info: (message, meta) => console.info(`[${plugin.name}] ${message}`, meta),
      warn: (message, meta) => console.warn(`[${plugin.name}] ${message}`, meta),
      error: (message, meta) => console.error(`[${plugin.name}] ${message}`, meta)
    }

    const api: PluginAPI = {
      getPipeline: async (id) => {
        // Implementation would check permissions and call actual API
        throw new Error('Not implemented')
      },
      updatePipeline: async (id, updates) => {
        // Implementation would check permissions and call actual API
        throw new Error('Not implemented')
      },
      getExecution: async (id) => {
        // Implementation would check permissions and call actual API
        throw new Error('Not implemented')
      },
      logMessage: async (executionId, message, level) => {
        // Implementation would log to execution
        console.log(`[${plugin.name}] ${level || 'info'}: ${message}`)
      },
      getSecret: async (name) => {
        // Implementation would check permissions and retrieve secret
        throw new Error('Not implemented')
      },
      fetch: async (url, options) => {
        // Implementation would check network permissions
        if (!this.hasPermission(plugin, 'network', url)) {
          throw new Error('Network access not permitted')
        }
        return fetch(url, options)
      }
    }

    const storage: PluginStorage = {
      get: async (key) => {
        const storageKey = `plugin:${plugin.id}:${key}`
        return JSON.parse(localStorage.getItem(storageKey) || 'null')
      },
      set: async (key, value) => {
        const storageKey = `plugin:${plugin.id}:${key}`
        localStorage.setItem(storageKey, JSON.stringify(value))
      },
      delete: async (key) => {
        const storageKey = `plugin:${plugin.id}:${key}`
        localStorage.removeItem(storageKey)
      },
      list: async () => {
        const prefix = `plugin:${plugin.id}:`
        const keys = []
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key?.startsWith(prefix)) {
            keys.push(key.substring(prefix.length))
          }
        }
        return keys
      }
    }

    return {
      plugin,
      config: plugin.config?.current || {},
      logger,
      api,
      storage
    }
  }

  private async loadPlugin(plugin: Plugin, context: PluginContext): Promise<void> {
    try {
      // Create sandboxed execution environment
      const sandbox = {
        console: context.logger,
        context,
        require: (module: string) => {
          // Whitelist allowed modules
          const allowedModules = ['lodash', 'moment', 'axios']
          if (!allowedModules.includes(module)) {
            throw new Error(`Module not allowed: ${module}`)
          }
          // Return mock or actual module
          return {}
        }
      }

      // Execute plugin code in sandbox
      const pluginFunction = new Function('sandbox', `
        with (sandbox) {
          ${plugin.code}
          return ${plugin.manifest.entryPoint};
        }
      `)

      const pluginModule = pluginFunction(sandbox)
      
      // Store plugin module for later use
      context.plugin.code = pluginModule

    } catch (error) {
      throw new Error(`Failed to load plugin: ${error}`)
    }
  }

  private async executePluginFunction(plugin: Plugin, context: PluginContext, functionName: string, ...args: any[]): Promise<any> {
    try {
      const pluginModule = context.plugin.code
      if (typeof pluginModule[functionName] === 'function') {
        return await pluginModule[functionName](...args)
      }
    } catch (error) {
      context.logger.error(`Error executing ${functionName}:`, error)
      throw error
    }
  }

  private async registerPluginComponents(plugin: Plugin, context: PluginContext): Promise<void> {
    // Register node executors
    if (plugin.manifest.nodeTypes) {
      for (const nodeType of plugin.manifest.nodeTypes) {
        this.nodeExecutors.set(nodeType, {
          execute: async (node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution) => {
            return this.executePluginFunction(plugin, context, 'executeNode', node, execution, nodeExecution)
          }
        })
      }
    }

    // Register hooks
    if (plugin.manifest.hooks) {
      for (const hookName of plugin.manifest.hooks) {
        if (!this.hooks.has(hookName)) {
          this.hooks.set(hookName, [])
        }
        this.hooks.get(hookName)!.push(async (data: any) => {
          return this.executePluginFunction(plugin, context, `on${hookName}`, data)
        })
      }
    }
  }

  private unregisterPluginComponents(plugin: Plugin): void {
    // Unregister node executors
    if (plugin.manifest.nodeTypes) {
      plugin.manifest.nodeTypes.forEach(type => {
        this.nodeExecutors.delete(type)
      })
    }

    // Unregister hooks
    if (plugin.manifest.hooks) {
      plugin.manifest.hooks.forEach(hookName => {
        this.hooks.delete(hookName)
      })
    }
  }

  private isPermissionAllowed(permission: PluginPermission): boolean {
    // Define allowed permissions
    const allowedPermissions = [
      'network:*',
      'filesystem:read',
      'secrets:read',
      'pipelines:read',
      'pipelines:write',
      'deployments:read'
    ]

    const permissionString = `${permission.type}:${permission.scope}`
    return allowedPermissions.some(allowed => {
      if (allowed.endsWith('*')) {
        return permissionString.startsWith(allowed.slice(0, -1))
      }
      return allowed === permissionString
    })
  }

  private hasPermission(plugin: Plugin, type: string, scope: string): boolean {
    return plugin.permissions.some(p => 
      p.type === type && (p.scope === '*' || p.scope === scope)
    )
  }
}

// Export singleton instance
export const pluginSystem = new PluginSystem()
