import { 
  Pipeline, 
  PipelineExecution, 
  NodeExecution, 
  ExecutionLog,
  PipelineNode 
} from '@/types/pipeline'
import { generateId } from '@/lib/utils'

export class ExecutionEngine {
  private executions = new Map<string, PipelineExecution>()
  private nodeExecutors = new Map<string, NodeExecutor>()
  
  constructor() {
    this.registerDefaultExecutors()
  }

  async executePipeline(
    pipeline: Pipeline, 
    variables: Record<string, any> = {},
    triggeredBy: PipelineExecution['triggeredBy']
  ): Promise<PipelineExecution> {
    const execution: PipelineExecution = {
      id: generateId(),
      pipelineId: pipeline.id,
      status: 'pending',
      startTime: new Date(),
      triggeredBy,
      nodeExecutions: [],
      logs: [],
      artifacts: [],
      variables: { ...pipeline.variables, ...variables }
    }

    this.executions.set(execution.id, execution)
    
    try {
      await this.runExecution(execution, pipeline)
    } catch (error) {
      execution.status = 'failed'
      execution.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }
    } finally {
      execution.endTime = new Date()
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
    }

    return execution
  }

  async stopExecution(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId)
    if (!execution) {
      throw new Error('Execution not found')
    }

    execution.status = 'cancelled'
    execution.endTime = new Date()
    execution.duration = execution.endTime.getTime() - execution.startTime.getTime()

    this.log(execution, 'info', 'Execution cancelled by user')
  }

  getExecution(executionId: string): PipelineExecution | undefined {
    return this.executions.get(executionId)
  }

  private async runExecution(execution: PipelineExecution, pipeline: Pipeline): Promise<void> {
    execution.status = 'running'
    this.log(execution, 'info', 'Pipeline execution started')

    // Build execution graph
    const executionGraph = this.buildExecutionGraph(pipeline)
    
    // Execute nodes in topological order
    const executedNodes = new Set<string>()
    const pendingNodes = new Set(pipeline.nodes.map(n => n.id))

    while (pendingNodes.size > 0 && execution.status === 'running') {
      const readyNodes = Array.from(pendingNodes).filter(nodeId => {
        const node = pipeline.nodes.find(n => n.id === nodeId)!
        const dependencies = executionGraph.dependencies.get(nodeId) || []
        return dependencies.every(depId => executedNodes.has(depId))
      })

      if (readyNodes.length === 0) {
        throw new Error('Circular dependency detected or no ready nodes')
      }

      // Execute ready nodes in parallel
      const nodePromises = readyNodes.map(nodeId => 
        this.executeNode(execution, pipeline.nodes.find(n => n.id === nodeId)!)
      )

      const results = await Promise.allSettled(nodePromises)
      
      for (let i = 0; i < results.length; i++) {
        const nodeId = readyNodes[i]
        const result = results[i]
        
        if (result.status === 'fulfilled') {
          executedNodes.add(nodeId)
          pendingNodes.delete(nodeId)
        } else {
          execution.status = 'failed'
          execution.error = {
            message: `Node ${nodeId} failed: ${result.reason}`,
            nodeId
          }
          return
        }
      }
    }

    if (execution.status === 'running') {
      execution.status = 'success'
      this.log(execution, 'info', 'Pipeline execution completed successfully')
    }
  }

  private async executeNode(execution: PipelineExecution, node: PipelineNode): Promise<void> {
    const nodeExecution: NodeExecution = {
      id: generateId(),
      nodeId: node.id,
      status: 'pending',
      logs: [],
      artifacts: [],
      retryCount: 0,
      maxRetries: 3
    }

    execution.nodeExecutions.push(nodeExecution)
    
    this.log(execution, 'info', `Starting execution of node: ${node.label}`, node.id)

    try {
      nodeExecution.status = 'running'
      nodeExecution.startTime = new Date()

      const executor = this.nodeExecutors.get(node.type)
      if (!executor) {
        throw new Error(`No executor found for node type: ${node.type}`)
      }

      await executor.execute(node, execution, nodeExecution)
      
      nodeExecution.status = 'success'
      this.log(execution, 'info', `Node completed successfully: ${node.label}`, node.id)
      
    } catch (error) {
      nodeExecution.status = 'failed'
      nodeExecution.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'EXECUTION_FAILED'
      }
      
      this.log(execution, 'error', `Node failed: ${node.label} - ${nodeExecution.error.message}`, node.id)
      throw error
      
    } finally {
      nodeExecution.endTime = new Date()
      nodeExecution.duration = nodeExecution.endTime.getTime() - (nodeExecution.startTime?.getTime() || 0)
    }
  }

  private buildExecutionGraph(pipeline: Pipeline) {
    const dependencies = new Map<string, string[]>()
    
    // Initialize all nodes with empty dependencies
    pipeline.nodes.forEach(node => {
      dependencies.set(node.id, [])
    })
    
    // Build dependency graph from edges
    pipeline.edges.forEach(edge => {
      const deps = dependencies.get(edge.target) || []
      deps.push(edge.source)
      dependencies.set(edge.target, deps)
    })
    
    return { dependencies }
  }

  private log(
    execution: PipelineExecution, 
    level: ExecutionLog['level'], 
    message: string, 
    nodeId?: string
  ): void {
    const log: ExecutionLog = {
      id: generateId(),
      timestamp: new Date(),
      level,
      message,
      nodeId,
      source: 'execution-engine'
    }
    
    execution.logs.push(log)
  }

  private registerDefaultExecutors(): void {
    // Git node executor
    this.nodeExecutors.set('git', new GitExecutor())
    
    // Build node executor
    this.nodeExecutors.set('build', new BuildExecutor())
    
    // Test node executor
    this.nodeExecutors.set('test', new TestExecutor())
    
    // Deploy node executors
    this.nodeExecutors.set('deploy-server', new ServerDeployExecutor())
    this.nodeExecutors.set('deploy-cloud', new CloudDeployExecutor())
    this.nodeExecutors.set('deploy-k8s', new KubernetesDeployExecutor())
    
    // Notification executors
    this.nodeExecutors.set('email', new EmailExecutor())
    this.nodeExecutors.set('slack', new SlackExecutor())
    
    // Utility executors
    this.nodeExecutors.set('script', new ScriptExecutor())
    this.nodeExecutors.set('condition', new ConditionExecutor())
  }
}

// Base executor interface
interface NodeExecutor {
  execute(
    node: PipelineNode, 
    execution: PipelineExecution, 
    nodeExecution: NodeExecution
  ): Promise<void>
}

// Example executor implementations
class GitExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { repository, branch = 'main' } = node.data.config
    
    // Simulate git clone
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    nodeExecution.logs.push(`Cloning repository: ${repository}`)
    nodeExecution.logs.push(`Checked out branch: ${branch}`)
    nodeExecution.artifacts.push('source-code')
  }
}

class BuildExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { command = 'npm run build' } = node.data.config
    
    // Simulate build process
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    nodeExecution.logs.push(`Running build command: ${command}`)
    nodeExecution.logs.push('Build completed successfully')
    nodeExecution.artifacts.push('build-artifacts')
  }
}

class TestExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { command = 'npm test' } = node.data.config
    
    // Simulate test execution
    await new Promise(resolve => setTimeout(resolve, 2500))
    
    nodeExecution.logs.push(`Running tests: ${command}`)
    nodeExecution.logs.push('All tests passed')
    nodeExecution.artifacts.push('test-results')
  }
}

class ServerDeployExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { server, path } = node.data.config
    
    // Simulate deployment
    await new Promise(resolve => setTimeout(resolve, 4000))
    
    nodeExecution.logs.push(`Deploying to server: ${server}`)
    nodeExecution.logs.push(`Deployment path: ${path}`)
    nodeExecution.logs.push('Deployment completed successfully')
  }
}

class CloudDeployExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { provider, service } = node.data.config
    
    // Simulate cloud deployment
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    nodeExecution.logs.push(`Deploying to ${provider} ${service}`)
    nodeExecution.logs.push('Cloud deployment completed')
  }
}

class KubernetesDeployExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { cluster, namespace = 'default' } = node.data.config
    
    // Simulate Kubernetes deployment
    await new Promise(resolve => setTimeout(resolve, 3500))
    
    nodeExecution.logs.push(`Deploying to Kubernetes cluster: ${cluster}`)
    nodeExecution.logs.push(`Namespace: ${namespace}`)
    nodeExecution.logs.push('Kubernetes deployment completed')
  }
}

class EmailExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { to, subject, body } = node.data.config
    
    // Simulate email sending
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    nodeExecution.logs.push(`Sending email to: ${to}`)
    nodeExecution.logs.push(`Subject: ${subject}`)
    nodeExecution.logs.push('Email sent successfully')
  }
}

class SlackExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { channel, message } = node.data.config
    
    // Simulate Slack message
    await new Promise(resolve => setTimeout(resolve, 800))
    
    nodeExecution.logs.push(`Sending Slack message to: ${channel}`)
    nodeExecution.logs.push('Slack message sent successfully')
  }
}

class ScriptExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { script, interpreter = 'bash' } = node.data.config
    
    // Simulate script execution
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    nodeExecution.logs.push(`Executing ${interpreter} script`)
    nodeExecution.logs.push('Script executed successfully')
  }
}

class ConditionExecutor implements NodeExecutor {
  async execute(node: PipelineNode, execution: PipelineExecution, nodeExecution: NodeExecution): Promise<void> {
    const { condition } = node.data.config
    
    // Simulate condition evaluation
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Mock condition result
    const result = Math.random() > 0.5
    
    nodeExecution.logs.push(`Evaluating condition: ${condition}`)
    nodeExecution.logs.push(`Condition result: ${result}`)
    
    if (!result) {
      throw new Error('Condition not met')
    }
  }
}

// Export singleton instance
export const executionEngine = new ExecutionEngine()
