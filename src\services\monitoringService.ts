import { PipelineExecution, ExecutionLog } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

export interface Metric {
  id: string
  name: string
  value: number
  unit: string
  timestamp: Date
  tags: Record<string, string>
}

export interface Alert {
  id: string
  type: 'error' | 'warning' | 'info'
  title: string
  message: string
  source: string
  timestamp: Date
  acknowledged: boolean
  resolvedAt?: Date
  metadata?: Record<string, any>
}

export interface HealthCheck {
  id: string
  name: string
  status: 'healthy' | 'unhealthy' | 'unknown'
  lastCheck: Date
  responseTime: number
  error?: string
}

export class MonitoringService {
  private metrics: Metric[] = []
  private alerts: Alert[] = []
  private healthChecks: HealthCheck[] = []
  private subscribers: ((event: MonitoringEvent) => void)[] = []

  // Metrics collection
  recordMetric(name: string, value: number, unit: string, tags: Record<string, string> = {}): void {
    const metric: Metric = {
      id: generateId(),
      name,
      value,
      unit,
      timestamp: new Date(),
      tags
    }

    this.metrics.push(metric)
    this.emit('metric', metric)

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  getMetrics(name?: string, timeRange?: { start: Date; end: Date }): Metric[] {
    let filtered = this.metrics

    if (name) {
      filtered = filtered.filter(m => m.name === name)
    }

    if (timeRange) {
      filtered = filtered.filter(m => 
        m.timestamp >= timeRange.start && m.timestamp <= timeRange.end
      )
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  getMetricsSummary(name: string, timeRange: { start: Date; end: Date }) {
    const metrics = this.getMetrics(name, timeRange)
    
    if (metrics.length === 0) {
      return null
    }

    const values = metrics.map(m => m.value)
    
    return {
      count: metrics.length,
      min: Math.min(...values),
      max: Math.max(...values),
      avg: values.reduce((sum, val) => sum + val, 0) / values.length,
      latest: metrics[0].value
    }
  }

  // Alerting
  createAlert(
    type: Alert['type'], 
    title: string, 
    message: string, 
    source: string,
    metadata?: Record<string, any>
  ): Alert {
    const alert: Alert = {
      id: generateId(),
      type,
      title,
      message,
      source,
      timestamp: new Date(),
      acknowledged: false,
      metadata
    }

    this.alerts.push(alert)
    this.emit('alert', alert)

    // Auto-send notifications based on alert type
    this.sendAlertNotification(alert)

    return alert
  }

  acknowledgeAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
      this.emit('alert_acknowledged', alert)
    }
  }

  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.resolvedAt = new Date()
      this.emit('alert_resolved', alert)
    }
  }

  getAlerts(filters?: {
    type?: Alert['type']
    acknowledged?: boolean
    resolved?: boolean
    source?: string
  }): Alert[] {
    let filtered = this.alerts

    if (filters) {
      if (filters.type) {
        filtered = filtered.filter(a => a.type === filters.type)
      }
      if (filters.acknowledged !== undefined) {
        filtered = filtered.filter(a => a.acknowledged === filters.acknowledged)
      }
      if (filters.resolved !== undefined) {
        const hasResolved = (alert: Alert) => !!alert.resolvedAt
        filtered = filtered.filter(a => hasResolved(a) === filters.resolved)
      }
      if (filters.source) {
        filtered = filtered.filter(a => a.source === filters.source)
      }
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  // Health checks
  registerHealthCheck(name: string, checkFn: () => Promise<{ healthy: boolean; responseTime: number; error?: string }>): void {
    const performCheck = async () => {
      const startTime = Date.now()
      
      try {
        const result = await checkFn()
        const responseTime = Date.now() - startTime

        const healthCheck: HealthCheck = {
          id: generateId(),
          name,
          status: result.healthy ? 'healthy' : 'unhealthy',
          lastCheck: new Date(),
          responseTime: result.responseTime || responseTime,
          error: result.error
        }

        // Update or add health check
        const existingIndex = this.healthChecks.findIndex(hc => hc.name === name)
        if (existingIndex >= 0) {
          this.healthChecks[existingIndex] = healthCheck
        } else {
          this.healthChecks.push(healthCheck)
        }

        this.emit('health_check', healthCheck)

        // Create alert if unhealthy
        if (!result.healthy) {
          this.createAlert(
            'error',
            `Health Check Failed: ${name}`,
            result.error || 'Health check returned unhealthy status',
            'health_check',
            { healthCheckName: name, responseTime: healthCheck.responseTime }
          )
        }

      } catch (error) {
        const healthCheck: HealthCheck = {
          id: generateId(),
          name,
          status: 'unhealthy',
          lastCheck: new Date(),
          responseTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error'
        }

        const existingIndex = this.healthChecks.findIndex(hc => hc.name === name)
        if (existingIndex >= 0) {
          this.healthChecks[existingIndex] = healthCheck
        } else {
          this.healthChecks.push(healthCheck)
        }

        this.emit('health_check', healthCheck)

        this.createAlert(
          'error',
          `Health Check Error: ${name}`,
          healthCheck.error || 'Health check threw an exception',
          'health_check',
          { healthCheckName: name, responseTime: healthCheck.responseTime }
        )
      }
    }

    // Perform initial check
    performCheck()

    // Schedule periodic checks every 5 minutes
    setInterval(performCheck, 5 * 60 * 1000)
  }

  getHealthChecks(): HealthCheck[] {
    return this.healthChecks.sort((a, b) => b.lastCheck.getTime() - a.lastCheck.getTime())
  }

  getSystemHealth(): {
    overall: 'healthy' | 'degraded' | 'unhealthy'
    checks: HealthCheck[]
    summary: {
      total: number
      healthy: number
      unhealthy: number
      unknown: number
    }
  } {
    const checks = this.getHealthChecks()
    const summary = {
      total: checks.length,
      healthy: checks.filter(hc => hc.status === 'healthy').length,
      unhealthy: checks.filter(hc => hc.status === 'unhealthy').length,
      unknown: checks.filter(hc => hc.status === 'unknown').length
    }

    let overall: 'healthy' | 'degraded' | 'unhealthy'
    if (summary.unhealthy > 0) {
      overall = summary.unhealthy > summary.healthy ? 'unhealthy' : 'degraded'
    } else {
      overall = 'healthy'
    }

    return { overall, checks, summary }
  }

  // Pipeline monitoring
  monitorPipelineExecution(execution: PipelineExecution): void {
    // Record execution metrics
    this.recordMetric('pipeline.execution.started', 1, 'count', {
      pipelineId: execution.pipelineId,
      triggeredBy: execution.triggeredBy.type
    })

    // Monitor execution progress
    const checkExecution = () => {
      if (execution.status === 'running') {
        const duration = Date.now() - execution.startTime.getTime()
        this.recordMetric('pipeline.execution.duration', duration, 'ms', {
          pipelineId: execution.pipelineId,
          status: 'running'
        })

        // Check for long-running executions (> 30 minutes)
        if (duration > 30 * 60 * 1000) {
          this.createAlert(
            'warning',
            'Long Running Pipeline',
            `Pipeline ${execution.pipelineId} has been running for ${Math.round(duration / 60000)} minutes`,
            'pipeline_monitor',
            { executionId: execution.id, pipelineId: execution.pipelineId, duration }
          )
        }

        setTimeout(checkExecution, 60000) // Check every minute
      } else {
        // Execution completed
        const duration = execution.duration || 0
        this.recordMetric('pipeline.execution.completed', 1, 'count', {
          pipelineId: execution.pipelineId,
          status: execution.status
        })
        this.recordMetric('pipeline.execution.duration', duration, 'ms', {
          pipelineId: execution.pipelineId,
          status: execution.status
        })

        if (execution.status === 'failed') {
          this.createAlert(
            'error',
            'Pipeline Execution Failed',
            `Pipeline ${execution.pipelineId} failed: ${execution.error?.message || 'Unknown error'}`,
            'pipeline_monitor',
            { executionId: execution.id, pipelineId: execution.pipelineId, error: execution.error }
          )
        }
      }
    }

    checkExecution()
  }

  // Event system
  subscribe(callback: (event: MonitoringEvent) => void): () => void {
    this.subscribers.push(callback)
    return () => {
      const index = this.subscribers.indexOf(callback)
      if (index >= 0) {
        this.subscribers.splice(index, 1)
      }
    }
  }

  private emit(type: string, data: any): void {
    const event: MonitoringEvent = { type, data, timestamp: new Date() }
    this.subscribers.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in monitoring event subscriber:', error)
      }
    })
  }

  private async sendAlertNotification(alert: Alert): Promise<void> {
    // In a real implementation, this would send notifications via:
    // - Email
    // - Slack
    // - Webhooks
    // - SMS
    // etc.

    console.log(`[ALERT] ${alert.type.toUpperCase()}: ${alert.title}`)
    console.log(`Message: ${alert.message}`)
    console.log(`Source: ${alert.source}`)
    console.log(`Time: ${alert.timestamp.toISOString()}`)

    // Simulate notification sending
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  // Reporting
  generateReport(timeRange: { start: Date; end: Date }) {
    const executions = this.getMetrics('pipeline.execution.completed', timeRange)
    const failures = executions.filter(m => m.tags.status === 'failed')
    const successes = executions.filter(m => m.tags.status === 'success')
    
    const alerts = this.getAlerts().filter(a => 
      a.timestamp >= timeRange.start && a.timestamp <= timeRange.end
    )

    const avgDuration = this.getMetricsSummary('pipeline.execution.duration', timeRange)

    return {
      timeRange,
      executions: {
        total: executions.length,
        successful: successes.length,
        failed: failures.length,
        successRate: executions.length > 0 ? (successes.length / executions.length) * 100 : 0
      },
      performance: {
        averageDuration: avgDuration?.avg || 0,
        minDuration: avgDuration?.min || 0,
        maxDuration: avgDuration?.max || 0
      },
      alerts: {
        total: alerts.length,
        errors: alerts.filter(a => a.type === 'error').length,
        warnings: alerts.filter(a => a.type === 'warning').length,
        unresolved: alerts.filter(a => !a.resolvedAt).length
      },
      health: this.getSystemHealth()
    }
  }
}

export interface MonitoringEvent {
  type: string
  data: any
  timestamp: Date
}

// Export singleton instance
export const monitoringService = new MonitoringService()

// Initialize default health checks
monitoringService.registerHealthCheck('system', async () => {
  // Mock system health check
  return {
    healthy: Math.random() > 0.1, // 90% healthy
    responseTime: Math.random() * 100 + 50 // 50-150ms
  }
})

monitoringService.registerHealthCheck('database', async () => {
  // Mock database health check
  return {
    healthy: Math.random() > 0.05, // 95% healthy
    responseTime: Math.random() * 50 + 10 // 10-60ms
  }
})
