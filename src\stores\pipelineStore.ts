import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { 
  Pipeline, 
  PipelineExecution, 
  PipelineNode, 
  PipelineEdge,
  PipelineValidationResult 
} from '@/types/pipeline'
import { generateId } from '@/lib/utils'

interface PipelineState {
  // Current pipeline being edited
  currentPipeline: Pipeline | null
  
  // All pipelines
  pipelines: Pipeline[]
  
  // Pipeline executions
  executions: PipelineExecution[]
  
  // UI state
  selectedNodes: string[]
  selectedEdges: string[]
  isExecuting: boolean
  validationResult: PipelineValidationResult | null
  
  // Actions
  createPipeline: (name: string, description?: string) => Pipeline
  loadPipeline: (id: string) => void
  savePipeline: (pipeline: Pipeline) => void
  deletePipeline: (id: string) => void
  
  // Node operations
  addNode: (node: Omit<PipelineNode, 'id'>) => void
  updateNode: (id: string, updates: Partial<PipelineNode>) => void
  deleteNode: (id: string) => void
  
  // Edge operations
  addEdge: (edge: Omit<PipelineEdge, 'id'>) => void
  updateEdge: (id: string, updates: Partial<PipelineEdge>) => void
  deleteEdge: (id: string) => void
  
  // Selection
  selectNodes: (nodeIds: string[]) => void
  selectEdges: (edgeIds: string[]) => void
  clearSelection: () => void
  
  // Execution
  executePipeline: (pipelineId: string, variables?: Record<string, any>) => Promise<PipelineExecution>
  stopExecution: (executionId: string) => Promise<void>
  
  // Validation
  validatePipeline: (pipeline: Pipeline) => PipelineValidationResult
  
  // Import/Export
  exportPipeline: (pipelineId: string) => string
  importPipeline: (data: string) => Pipeline
}

export const usePipelineStore = create<PipelineState>()(
  devtools(
    persist(
      (set, get) => ({
        currentPipeline: null,
        pipelines: [],
        executions: [],
        selectedNodes: [],
        selectedEdges: [],
        isExecuting: false,
        validationResult: null,

        createPipeline: (name: string, description?: string) => {
          const pipeline: Pipeline = {
            id: generateId(),
            name,
            description,
            version: '1.0.0',
            nodes: [],
            edges: [],
            variables: {},
            secrets: [],
            triggers: [],
            status: 'draft',
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: 'current-user', // TODO: Get from auth
            tags: [],
            metadata: {}
          }
          
          set((state) => ({
            pipelines: [...state.pipelines, pipeline],
            currentPipeline: pipeline
          }))
          
          return pipeline
        },

        loadPipeline: (id: string) => {
          const pipeline = get().pipelines.find(p => p.id === id)
          if (pipeline) {
            set({ currentPipeline: pipeline })
          }
        },

        savePipeline: (pipeline: Pipeline) => {
          const updatedPipeline = {
            ...pipeline,
            updatedAt: new Date()
          }
          
          set((state) => ({
            pipelines: state.pipelines.map(p => 
              p.id === pipeline.id ? updatedPipeline : p
            ),
            currentPipeline: state.currentPipeline?.id === pipeline.id 
              ? updatedPipeline 
              : state.currentPipeline
          }))
        },

        deletePipeline: (id: string) => {
          set((state) => ({
            pipelines: state.pipelines.filter(p => p.id !== id),
            currentPipeline: state.currentPipeline?.id === id ? null : state.currentPipeline
          }))
        },

        addNode: (nodeData: Omit<PipelineNode, 'id'>) => {
          const node: PipelineNode = {
            ...nodeData,
            id: generateId()
          }
          
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              nodes: [...state.currentPipeline.nodes, node],
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        updateNode: (id: string, updates: Partial<PipelineNode>) => {
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              nodes: state.currentPipeline.nodes.map(node =>
                node.id === id ? { ...node, ...updates } : node
              ),
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        deleteNode: (id: string) => {
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              nodes: state.currentPipeline.nodes.filter(node => node.id !== id),
              edges: state.currentPipeline.edges.filter(edge => 
                edge.source !== id && edge.target !== id
              ),
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        addEdge: (edgeData: Omit<PipelineEdge, 'id'>) => {
          const edge: PipelineEdge = {
            ...edgeData,
            id: generateId()
          }
          
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              edges: [...state.currentPipeline.edges, edge],
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        updateEdge: (id: string, updates: Partial<PipelineEdge>) => {
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              edges: state.currentPipeline.edges.map(edge =>
                edge.id === id ? { ...edge, ...updates } : edge
              ),
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        deleteEdge: (id: string) => {
          set((state) => {
            if (!state.currentPipeline) return state
            
            const updatedPipeline = {
              ...state.currentPipeline,
              edges: state.currentPipeline.edges.filter(edge => edge.id !== id),
              updatedAt: new Date()
            }
            
            return {
              currentPipeline: updatedPipeline,
              pipelines: state.pipelines.map(p => 
                p.id === updatedPipeline.id ? updatedPipeline : p
              )
            }
          })
        },

        selectNodes: (nodeIds: string[]) => {
          set({ selectedNodes: nodeIds })
        },

        selectEdges: (edgeIds: string[]) => {
          set({ selectedEdges: edgeIds })
        },

        clearSelection: () => {
          set({ selectedNodes: [], selectedEdges: [] })
        },

        executePipeline: async (pipelineId: string, variables = {}) => {
          const pipeline = get().pipelines.find(p => p.id === pipelineId)
          if (!pipeline) {
            throw new Error('Pipeline not found')
          }

          const execution: PipelineExecution = {
            id: generateId(),
            pipelineId,
            status: 'pending',
            startTime: new Date(),
            triggeredBy: {
              type: 'manual',
              user: 'current-user' // TODO: Get from auth
            },
            nodeExecutions: [],
            logs: [],
            artifacts: [],
            variables
          }

          set((state) => ({
            executions: [...state.executions, execution],
            isExecuting: true
          }))

          // TODO: Implement actual execution logic
          // This would involve calling the execution engine
          
          return execution
        },

        stopExecution: async (executionId: string) => {
          // TODO: Implement execution stopping
          set({ isExecuting: false })
        },

        validatePipeline: (pipeline: Pipeline): PipelineValidationResult => {
          const errors = []
          const warnings = []

          // Basic validation
          if (pipeline.nodes.length === 0) {
            errors.push({
              id: generateId(),
              type: 'logic' as const,
              message: 'Pipeline must contain at least one node',
              severity: 'error' as const
            })
          }

          // Check for disconnected nodes
          const connectedNodes = new Set<string>()
          pipeline.edges.forEach(edge => {
            connectedNodes.add(edge.source)
            connectedNodes.add(edge.target)
          })

          pipeline.nodes.forEach(node => {
            if (!connectedNodes.has(node.id) && pipeline.nodes.length > 1) {
              warnings.push({
                id: generateId(),
                type: 'logic' as const,
                message: `Node "${node.label}" is not connected to the pipeline`,
                nodeId: node.id,
                suggestion: 'Connect this node to other nodes or remove it'
              })
            }
          })

          const result = {
            isValid: errors.length === 0,
            errors,
            warnings
          }

          set({ validationResult: result })
          return result
        },

        exportPipeline: (pipelineId: string): string => {
          const pipeline = get().pipelines.find(p => p.id === pipelineId)
          if (!pipeline) {
            throw new Error('Pipeline not found')
          }
          return JSON.stringify(pipeline, null, 2)
        },

        importPipeline: (data: string): Pipeline => {
          try {
            const pipelineData = JSON.parse(data)
            const pipeline: Pipeline = {
              ...pipelineData,
              id: generateId(),
              createdAt: new Date(),
              updatedAt: new Date(),
              createdBy: 'current-user' // TODO: Get from auth
            }
            
            set((state) => ({
              pipelines: [...state.pipelines, pipeline]
            }))
            
            return pipeline
          } catch (error) {
            throw new Error('Invalid pipeline data')
          }
        }
      }),
      {
        name: 'pipeline-store',
        partialize: (state) => ({
          pipelines: state.pipelines,
          executions: state.executions
        })
      }
    ),
    { name: 'pipeline-store' }
  )
)
