import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { 
  Pipeline, 
  PipelineExecution, 
  DeploymentTarget, 
  Secret, 
  Template 
} from '@/types/pipeline'

export class ApiService {
  private client: AxiosInstance

  constructor(baseURL: string = '/api') {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor for auth
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          localStorage.removeItem('auth_token')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Pipeline API
  async getPipelines(): Promise<Pipeline[]> {
    const response = await this.client.get('/pipelines')
    return response.data
  }

  async getPipeline(id: string): Promise<Pipeline> {
    const response = await this.client.get(`/pipelines/${id}`)
    return response.data
  }

  async createPipeline(pipeline: Omit<Pipeline, 'id' | 'createdAt' | 'updatedAt'>): Promise<Pipeline> {
    const response = await this.client.post('/pipelines', pipeline)
    return response.data
  }

  async updatePipeline(id: string, pipeline: Partial<Pipeline>): Promise<Pipeline> {
    const response = await this.client.put(`/pipelines/${id}`, pipeline)
    return response.data
  }

  async deletePipeline(id: string): Promise<void> {
    await this.client.delete(`/pipelines/${id}`)
  }

  async validatePipeline(pipeline: Pipeline): Promise<any> {
    const response = await this.client.post('/pipelines/validate', pipeline)
    return response.data
  }

  // Execution API
  async getExecutions(pipelineId?: string): Promise<PipelineExecution[]> {
    const params = pipelineId ? { pipelineId } : {}
    const response = await this.client.get('/executions', { params })
    return response.data
  }

  async getExecution(id: string): Promise<PipelineExecution> {
    const response = await this.client.get(`/executions/${id}`)
    return response.data
  }

  async executePipeline(
    pipelineId: string, 
    variables?: Record<string, any>
  ): Promise<PipelineExecution> {
    const response = await this.client.post(`/pipelines/${pipelineId}/execute`, { variables })
    return response.data
  }

  async stopExecution(executionId: string): Promise<void> {
    await this.client.post(`/executions/${executionId}/stop`)
  }

  async getExecutionLogs(executionId: string): Promise<any[]> {
    const response = await this.client.get(`/executions/${executionId}/logs`)
    return response.data
  }

  // Deployment Targets API
  async getDeploymentTargets(): Promise<DeploymentTarget[]> {
    const response = await this.client.get('/deployment-targets')
    return response.data
  }

  async getDeploymentTarget(id: string): Promise<DeploymentTarget> {
    const response = await this.client.get(`/deployment-targets/${id}`)
    return response.data
  }

  async createDeploymentTarget(
    target: Omit<DeploymentTarget, 'id' | 'createdAt' | 'updatedAt' | 'deploymentCount'>
  ): Promise<DeploymentTarget> {
    const response = await this.client.post('/deployment-targets', target)
    return response.data
  }

  async updateDeploymentTarget(id: string, target: Partial<DeploymentTarget>): Promise<DeploymentTarget> {
    const response = await this.client.put(`/deployment-targets/${id}`, target)
    return response.data
  }

  async deleteDeploymentTarget(id: string): Promise<void> {
    await this.client.delete(`/deployment-targets/${id}`)
  }

  async testDeploymentTarget(id: string): Promise<{ success: boolean; message: string }> {
    const response = await this.client.post(`/deployment-targets/${id}/test`)
    return response.data
  }

  // Secrets API
  async getSecrets(): Promise<Omit<Secret, 'value'>[]> {
    const response = await this.client.get('/secrets')
    return response.data
  }

  async getSecret(id: string): Promise<Omit<Secret, 'value'>> {
    const response = await this.client.get(`/secrets/${id}`)
    return response.data
  }

  async createSecret(secret: Omit<Secret, 'id' | 'createdAt' | 'updatedAt' | 'accessLog' | 'isExpired'>): Promise<Secret> {
    const response = await this.client.post('/secrets', secret)
    return response.data
  }

  async updateSecret(id: string, secret: Partial<Secret>): Promise<Secret> {
    const response = await this.client.put(`/secrets/${id}`, secret)
    return response.data
  }

  async deleteSecret(id: string): Promise<void> {
    await this.client.delete(`/secrets/${id}`)
  }

  async getSecretValue(id: string): Promise<string> {
    const response = await this.client.get(`/secrets/${id}/value`)
    return response.data.value
  }

  // Templates API
  async getTemplates(): Promise<Template[]> {
    const response = await this.client.get('/templates')
    return response.data
  }

  async getTemplate(id: string): Promise<Template> {
    const response = await this.client.get(`/templates/${id}`)
    return response.data
  }

  async createTemplate(template: Omit<Template, 'id' | 'createdAt' | 'updatedAt' | 'downloads' | 'rating' | 'reviews'>): Promise<Template> {
    const response = await this.client.post('/templates', template)
    return response.data
  }

  async updateTemplate(id: string, template: Partial<Template>): Promise<Template> {
    const response = await this.client.put(`/templates/${id}`, template)
    return response.data
  }

  async deleteTemplate(id: string): Promise<void> {
    await this.client.delete(`/templates/${id}`)
  }

  async instantiateTemplate(id: string, parameters: Record<string, any>): Promise<Pipeline> {
    const response = await this.client.post(`/templates/${id}/instantiate`, { parameters })
    return response.data
  }

  // Monitoring API
  async getMetrics(params?: {
    name?: string
    start?: Date
    end?: Date
    tags?: Record<string, string>
  }): Promise<any[]> {
    const response = await this.client.get('/metrics', { params })
    return response.data
  }

  async getAlerts(params?: {
    type?: string
    acknowledged?: boolean
    resolved?: boolean
  }): Promise<any[]> {
    const response = await this.client.get('/alerts', { params })
    return response.data
  }

  async acknowledgeAlert(alertId: string): Promise<void> {
    await this.client.post(`/alerts/${alertId}/acknowledge`)
  }

  async resolveAlert(alertId: string): Promise<void> {
    await this.client.post(`/alerts/${alertId}/resolve`)
  }

  async getHealthChecks(): Promise<any[]> {
    const response = await this.client.get('/health')
    return response.data
  }

  // File upload/download
  async uploadFile(file: File, path?: string): Promise<{ url: string; path: string }> {
    const formData = new FormData()
    formData.append('file', file)
    if (path) {
      formData.append('path', path)
    }

    const response = await this.client.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  async downloadFile(path: string): Promise<Blob> {
    const response = await this.client.get(`/files/download`, {
      params: { path },
      responseType: 'blob'
    })
    return response.data
  }

  // Webhooks
  async createWebhook(webhook: {
    name: string
    url: string
    events: string[]
    secret?: string
  }): Promise<any> {
    const response = await this.client.post('/webhooks', webhook)
    return response.data
  }

  async getWebhooks(): Promise<any[]> {
    const response = await this.client.get('/webhooks')
    return response.data
  }

  async deleteWebhook(id: string): Promise<void> {
    await this.client.delete(`/webhooks/${id}`)
  }

  // System info
  async getSystemInfo(): Promise<{
    version: string
    uptime: number
    stats: {
      pipelines: number
      executions: number
      users: number
    }
  }> {
    const response = await this.client.get('/system/info')
    return response.data
  }

  async getSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    checks: any[]
  }> {
    const response = await this.client.get('/system/health')
    return response.data
  }
}

// Export singleton instance
export const apiService = new ApiService()

// Mock API responses for development
if (process.env.NODE_ENV === 'development') {
  // Override API service methods with mock implementations
  const originalMethods = { ...apiService }
  
  // Mock pipeline operations
  apiService.getPipelines = async () => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return []
  }

  apiService.executePipeline = async (pipelineId: string, variables = {}) => {
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      id: 'exec-' + Date.now(),
      pipelineId,
      status: 'running' as const,
      startTime: new Date(),
      triggeredBy: { type: 'manual' as const, user: 'test-user' },
      nodeExecutions: [],
      logs: [],
      artifacts: [],
      variables
    }
  }

  // Add more mock implementations as needed
}
