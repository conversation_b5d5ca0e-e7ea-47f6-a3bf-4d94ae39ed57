import { Button } from '@/components/ui/button'
import { 
  Save, 
  Play, 
  Square, 
  RotateCcw, 
  Share, 
  Settings, 
  Download, 
  Upload,
  Zap,
  Eye
} from 'lucide-react'

interface PipelineToolbarProps {
  onSave: () => void
  onRun: () => void
  onStop: () => void
  onReset: () => void
  onShare: () => void
  onSettings: () => void
  onExport: () => void
  onImport: () => void
  onValidate: () => void
  onPreview: () => void
  isRunning?: boolean
  canStop?: boolean
}

export function PipelineToolbar({
  onSave,
  onRun,
  onStop,
  onReset,
  onShare,
  onSettings,
  onExport,
  onImport,
  onValidate,
  onPreview,
  isRunning = false,
  canStop = false,
}: PipelineToolbarProps) {
  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      <div className="flex items-center space-x-2">
        <Button onClick={onSave} variant="outline">
          <Save className="mr-2 h-4 w-4" />
          Save
        </Button>
        
        {!isRunning ? (
          <Button onClick={onRun}>
            <Play className="mr-2 h-4 w-4" />
            Run
          </Button>
        ) : (
          <Button onClick={onStop} variant="destructive" disabled={!canStop}>
            <Square className="mr-2 h-4 w-4" />
            Stop
          </Button>
        )}

        <Button onClick={onValidate} variant="outline">
          <Zap className="mr-2 h-4 w-4" />
          Validate
        </Button>

        <Button onClick={onPreview} variant="outline">
          <Eye className="mr-2 h-4 w-4" />
          Preview
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <Button onClick={onImport} variant="outline" size="sm">
          <Upload className="mr-2 h-4 w-4" />
          Import
        </Button>

        <Button onClick={onExport} variant="outline" size="sm">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>

        <Button onClick={onShare} variant="outline" size="sm">
          <Share className="mr-2 h-4 w-4" />
          Share
        </Button>

        <Button onClick={onReset} variant="outline" size="sm">
          <RotateCcw className="mr-2 h-4 w-4" />
          Reset
        </Button>

        <Button onClick={onSettings} variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Settings
        </Button>
      </div>
    </div>
  )
}
