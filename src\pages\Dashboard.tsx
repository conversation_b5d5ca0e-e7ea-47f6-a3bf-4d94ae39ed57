import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Activity, 
  GitBranch, 
  Server, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Plus,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { formatDate, formatDuration } from '@/lib/utils'

interface DashboardStats {
  totalPipelines: number
  activePipelines: number
  successfulRuns: number
  failedRuns: number
  deploymentTargets: number
  avgExecutionTime: number
}

interface RecentActivity {
  id: string
  type: 'pipeline_run' | 'deployment' | 'template_created'
  title: string
  status: 'success' | 'failed' | 'running' | 'pending'
  timestamp: Date
  duration?: number
}

export function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalPipelines: 12,
    activePipelines: 8,
    successfulRuns: 156,
    failedRuns: 12,
    deploymentTargets: 6,
    avgExecutionTime: 180000, // 3 minutes in ms
  })

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([
    {
      id: '1',
      type: 'pipeline_run',
      title: 'Frontend Build & Deploy',
      status: 'success',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      duration: 120000,
    },
    {
      id: '2',
      type: 'deployment',
      title: 'API Server Deployment',
      status: 'running',
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    },
    {
      id: '3',
      type: 'pipeline_run',
      title: 'Database Migration',
      status: 'failed',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      duration: 45000,
    },
    {
      id: '4',
      type: 'template_created',
      title: 'React App Template',
      status: 'success',
      timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
    },
  ])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'pipeline_run':
        return <GitBranch className="h-4 w-4" />
      case 'deployment':
        return <Server className="h-4 w-4" />
      case 'template_created':
        return <Plus className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your pipelines.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button asChild>
            <Link to="/designer">
              <Plus className="mr-2 h-4 w-4" />
              New Pipeline
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Pipelines</p>
              <p className="text-2xl font-bold">{stats.totalPipelines}</p>
            </div>
            <GitBranch className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Active Pipelines</p>
              <p className="text-2xl font-bold">{stats.activePipelines}</p>
            </div>
            <Activity className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
              <p className="text-2xl font-bold">
                {Math.round((stats.successfulRuns / (stats.successfulRuns + stats.failedRuns)) * 100)}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Avg. Execution</p>
              <p className="text-2xl font-bold">{formatDuration(stats.avgExecutionTime)}</p>
            </div>
            <Zap className="h-8 w-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="rounded-lg border bg-card">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Activity</h2>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 rounded-md hover:bg-accent">
                <div className="flex items-center space-x-3">
                  {getActivityTypeIcon(activity.type)}
                  <div>
                    <p className="font-medium">{activity.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(activity.timestamp)}
                      {activity.duration && ` • ${formatDuration(activity.duration)}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(activity.status)}
                  <span className="text-sm capitalize">{activity.status}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Link to="/designer" className="rounded-lg border bg-card p-6 hover:bg-accent transition-colors">
          <div className="flex items-center space-x-3">
            <GitBranch className="h-8 w-8 text-primary" />
            <div>
              <h3 className="font-semibold">Create Pipeline</h3>
              <p className="text-sm text-muted-foreground">Design a new automation pipeline</p>
            </div>
          </div>
        </Link>

        <Link to="/deployments" className="rounded-lg border bg-card p-6 hover:bg-accent transition-colors">
          <div className="flex items-center space-x-3">
            <Server className="h-8 w-8 text-primary" />
            <div>
              <h3 className="font-semibold">Manage Deployments</h3>
              <p className="text-sm text-muted-foreground">Configure deployment targets</p>
            </div>
          </div>
        </Link>

        <Link to="/templates" className="rounded-lg border bg-card p-6 hover:bg-accent transition-colors">
          <div className="flex items-center space-x-3">
            <Plus className="h-8 w-8 text-primary" />
            <div>
              <h3 className="font-semibold">Browse Templates</h3>
              <p className="text-sm text-muted-foreground">Start from pre-built templates</p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  )
}
