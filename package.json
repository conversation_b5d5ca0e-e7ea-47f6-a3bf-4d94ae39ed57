{"name": "pipeline-platform", "version": "1.0.0", "description": "Comprehensive pipeline automation platform with drag-and-drop interface", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "@tanstack/react-query": "^5.8.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "reactflow": "^11.10.0", "zustand": "^4.4.0", "axios": "^1.6.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "yaml": "^2.3.0", "zod": "^3.22.0", "class-variance-authority": "^0.7.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.0"}}