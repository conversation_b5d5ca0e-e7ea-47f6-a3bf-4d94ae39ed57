import { Pipeline, PipelineNode, PipelineExecution } from '@/types/pipeline'
import { generateId } from '@/lib/utils'

export interface TestCase {
  id: string
  name: string
  description: string
  type: 'unit' | 'integration' | 'load' | 'stress' | 'canary'
  pipelineId: string
  config: {
    inputs?: Record<string, any>
    expectedOutputs?: Record<string, any>
    timeout?: number
    retries?: number
    environment?: string
    mockServices?: string[]
  }
  assertions: TestAssertion[]
  createdAt: Date
  updatedAt: Date
}

export interface TestAssertion {
  id: string
  type: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists' | 'matches_regex'
  target: string // path to value to test (e.g., 'execution.status', 'node.build.artifacts[0]')
  expected: any
  description?: string
}

export interface TestExecution {
  id: string
  testCaseId: string
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped'
  startTime: Date
  endTime?: Date
  duration?: number
  results: TestResult[]
  logs: string[]
  error?: {
    message: string
    stack?: string
  }
  pipelineExecution?: PipelineExecution
}

export interface TestResult {
  assertionId: string
  passed: boolean
  actual: any
  expected: any
  message: string
}

export interface TestSuite {
  id: string
  name: string
  description: string
  testCases: string[] // test case IDs
  config: {
    parallel?: boolean
    stopOnFailure?: boolean
    environment?: string
    setup?: string[] // setup scripts
    teardown?: string[] // cleanup scripts
  }
  createdAt: Date
  updatedAt: Date
}

export interface TestReport {
  id: string
  suiteId?: string
  testCaseIds: string[]
  startTime: Date
  endTime: Date
  duration: number
  summary: {
    total: number
    passed: number
    failed: number
    skipped: number
    passRate: number
  }
  executions: TestExecution[]
  environment: string
  triggeredBy: string
}

export class TestingService {
  private testCases: TestCase[] = []
  private testSuites: TestSuite[] = []
  private testExecutions: TestExecution[] = []
  private testReports: TestReport[] = []

  // Test Case Management
  createTestCase(testCase: Omit<TestCase, 'id' | 'createdAt' | 'updatedAt'>): TestCase {
    const newTestCase: TestCase = {
      ...testCase,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.testCases.push(newTestCase)
    return newTestCase
  }

  updateTestCase(id: string, updates: Partial<TestCase>): TestCase | null {
    const index = this.testCases.findIndex(tc => tc.id === id)
    if (index === -1) return null

    this.testCases[index] = {
      ...this.testCases[index],
      ...updates,
      updatedAt: new Date()
    }

    return this.testCases[index]
  }

  deleteTestCase(id: string): boolean {
    const index = this.testCases.findIndex(tc => tc.id === id)
    if (index === -1) return false

    this.testCases.splice(index, 1)
    return true
  }

  getTestCases(pipelineId?: string): TestCase[] {
    if (pipelineId) {
      return this.testCases.filter(tc => tc.pipelineId === pipelineId)
    }
    return this.testCases
  }

  // Test Suite Management
  createTestSuite(testSuite: Omit<TestSuite, 'id' | 'createdAt' | 'updatedAt'>): TestSuite {
    const newTestSuite: TestSuite = {
      ...testSuite,
      id: generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    this.testSuites.push(newTestSuite)
    return newTestSuite
  }

  getTestSuites(): TestSuite[] {
    return this.testSuites
  }

  // Test Execution
  async executeTestCase(testCaseId: string): Promise<TestExecution> {
    const testCase = this.testCases.find(tc => tc.id === testCaseId)
    if (!testCase) {
      throw new Error('Test case not found')
    }

    const execution: TestExecution = {
      id: generateId(),
      testCaseId,
      status: 'pending',
      startTime: new Date(),
      results: [],
      logs: []
    }

    this.testExecutions.push(execution)

    try {
      execution.status = 'running'
      execution.logs.push(`Starting test case: ${testCase.name}`)

      // Execute the pipeline with test inputs
      const pipelineExecution = await this.executePipelineForTest(testCase)
      execution.pipelineExecution = pipelineExecution

      // Wait for pipeline completion
      await this.waitForPipelineCompletion(pipelineExecution, testCase.config.timeout || 300000)

      // Run assertions
      execution.results = await this.runAssertions(testCase.assertions, pipelineExecution)

      // Determine test status
      const failedAssertions = execution.results.filter(r => !r.passed)
      execution.status = failedAssertions.length === 0 ? 'passed' : 'failed'

      execution.logs.push(`Test completed with status: ${execution.status}`)
      if (failedAssertions.length > 0) {
        execution.logs.push(`Failed assertions: ${failedAssertions.length}`)
      }

    } catch (error) {
      execution.status = 'failed'
      execution.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }
      execution.logs.push(`Test failed with error: ${execution.error.message}`)
    } finally {
      execution.endTime = new Date()
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
    }

    return execution
  }

  async executeTestSuite(suiteId: string): Promise<TestReport> {
    const testSuite = this.testSuites.find(ts => ts.id === suiteId)
    if (!testSuite) {
      throw new Error('Test suite not found')
    }

    const report: TestReport = {
      id: generateId(),
      suiteId,
      testCaseIds: testSuite.testCases,
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        passRate: 0
      },
      executions: [],
      environment: testSuite.config.environment || 'test',
      triggeredBy: 'manual' // TODO: Get from auth context
    }

    try {
      // Run setup scripts
      if (testSuite.config.setup) {
        for (const setupScript of testSuite.config.setup) {
          await this.runScript(setupScript)
        }
      }

      // Execute test cases
      if (testSuite.config.parallel) {
        // Run tests in parallel
        const promises = testSuite.testCases.map(tcId => this.executeTestCase(tcId))
        report.executions = await Promise.all(promises)
      } else {
        // Run tests sequentially
        for (const testCaseId of testSuite.testCases) {
          const execution = await this.executeTestCase(testCaseId)
          report.executions.push(execution)

          // Stop on failure if configured
          if (testSuite.config.stopOnFailure && execution.status === 'failed') {
            break
          }
        }
      }

      // Calculate summary
      report.summary.total = report.executions.length
      report.summary.passed = report.executions.filter(e => e.status === 'passed').length
      report.summary.failed = report.executions.filter(e => e.status === 'failed').length
      report.summary.skipped = report.executions.filter(e => e.status === 'skipped').length
      report.summary.passRate = report.summary.total > 0 
        ? (report.summary.passed / report.summary.total) * 100 
        : 0

    } catch (error) {
      console.error('Test suite execution failed:', error)
    } finally {
      // Run teardown scripts
      if (testSuite.config.teardown) {
        for (const teardownScript of testSuite.config.teardown) {
          try {
            await this.runScript(teardownScript)
          } catch (error) {
            console.error('Teardown script failed:', error)
          }
        }
      }

      report.endTime = new Date()
      report.duration = report.endTime.getTime() - report.startTime.getTime()
    }

    this.testReports.push(report)
    return report
  }

  // Load Testing
  async executeLoadTest(testCaseId: string, config: {
    concurrency: number
    duration: number // seconds
    rampUp?: number // seconds
  }): Promise<TestReport> {
    const testCase = this.testCases.find(tc => tc.id === testCaseId)
    if (!testCase) {
      throw new Error('Test case not found')
    }

    const report: TestReport = {
      id: generateId(),
      testCaseIds: [testCaseId],
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        passRate: 0
      },
      executions: [],
      environment: 'load-test',
      triggeredBy: 'load-test'
    }

    const executions: Promise<TestExecution>[] = []
    const startTime = Date.now()
    const endTime = startTime + (config.duration * 1000)
    const rampUpTime = config.rampUp ? config.rampUp * 1000 : 0
    const rampUpInterval = rampUpTime / config.concurrency

    // Ramp up concurrent executions
    for (let i = 0; i < config.concurrency; i++) {
      setTimeout(() => {
        const executeUntilEnd = async () => {
          while (Date.now() < endTime) {
            executions.push(this.executeTestCase(testCaseId))
            await new Promise(resolve => setTimeout(resolve, 1000)) // 1 second between executions
          }
        }
        executeUntilEnd()
      }, i * rampUpInterval)
    }

    // Wait for all executions to complete
    setTimeout(async () => {
      report.executions = await Promise.all(executions)
      
      // Calculate summary
      report.summary.total = report.executions.length
      report.summary.passed = report.executions.filter(e => e.status === 'passed').length
      report.summary.failed = report.executions.filter(e => e.status === 'failed').length
      report.summary.skipped = report.executions.filter(e => e.status === 'skipped').length
      report.summary.passRate = report.summary.total > 0 
        ? (report.summary.passed / report.summary.total) * 100 
        : 0

      report.endTime = new Date()
      report.duration = report.endTime.getTime() - report.startTime.getTime()
      
      this.testReports.push(report)
    }, config.duration * 1000 + 5000) // Wait extra 5 seconds for cleanup

    return report
  }

  // Helper methods
  private async executePipelineForTest(testCase: TestCase): Promise<PipelineExecution> {
    // Mock pipeline execution for testing
    // In real implementation, this would trigger actual pipeline execution
    return {
      id: generateId(),
      pipelineId: testCase.pipelineId,
      status: 'running',
      startTime: new Date(),
      triggeredBy: { type: 'manual', user: 'test-runner' },
      nodeExecutions: [],
      logs: [],
      artifacts: [],
      variables: testCase.config.inputs || {}
    }
  }

  private async waitForPipelineCompletion(execution: PipelineExecution, timeout: number): Promise<void> {
    const startTime = Date.now()
    
    while (execution.status === 'running' || execution.status === 'pending') {
      if (Date.now() - startTime > timeout) {
        throw new Error('Pipeline execution timeout')
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock status update
      if (Math.random() > 0.9) {
        execution.status = Math.random() > 0.8 ? 'failed' : 'success'
        execution.endTime = new Date()
        execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
      }
    }
  }

  private async runAssertions(assertions: TestAssertion[], execution: PipelineExecution): Promise<TestResult[]> {
    const results: TestResult[] = []

    for (const assertion of assertions) {
      try {
        const actual = this.getValueByPath(execution, assertion.target)
        const passed = this.evaluateAssertion(assertion.type, actual, assertion.expected)

        results.push({
          assertionId: assertion.id,
          passed,
          actual,
          expected: assertion.expected,
          message: passed 
            ? `Assertion passed: ${assertion.description || assertion.target}`
            : `Assertion failed: ${assertion.description || assertion.target}. Expected ${assertion.expected}, got ${actual}`
        })
      } catch (error) {
        results.push({
          assertionId: assertion.id,
          passed: false,
          actual: null,
          expected: assertion.expected,
          message: `Assertion error: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }

    return results
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      if (current === null || current === undefined) return undefined
      
      // Handle array indices
      const arrayMatch = key.match(/^(.+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current[arrayKey]?.[parseInt(index)]
      }
      
      return current[key]
    }, obj)
  }

  private evaluateAssertion(type: TestAssertion['type'], actual: any, expected: any): boolean {
    switch (type) {
      case 'equals':
        return actual === expected
      case 'contains':
        return String(actual).includes(String(expected))
      case 'greater_than':
        return Number(actual) > Number(expected)
      case 'less_than':
        return Number(actual) < Number(expected)
      case 'exists':
        return actual !== null && actual !== undefined
      case 'not_exists':
        return actual === null || actual === undefined
      case 'matches_regex':
        return new RegExp(expected).test(String(actual))
      default:
        throw new Error(`Unknown assertion type: ${type}`)
    }
  }

  private async runScript(script: string): Promise<void> {
    // Mock script execution
    console.log(`Running script: ${script}`)
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  // Reporting
  getTestReports(): TestReport[] {
    return this.testReports.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
  }

  getTestExecutions(testCaseId?: string): TestExecution[] {
    if (testCaseId) {
      return this.testExecutions.filter(te => te.testCaseId === testCaseId)
    }
    return this.testExecutions
  }
}

// Export singleton instance
export const testingService = new TestingService()
