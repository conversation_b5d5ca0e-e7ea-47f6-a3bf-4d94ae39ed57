export interface PipelineNode {
  id: string
  type: string
  label: string
  position: { x: number; y: number }
  data: {
    config: Record<string, any>
    status?: 'idle' | 'pending' | 'running' | 'success' | 'failed'
    startTime?: Date
    endTime?: Date
    logs?: string[]
    artifacts?: string[]
  }
}

export interface PipelineEdge {
  id: string
  source: string
  target: string
  type?: string
  animated?: boolean
  data?: {
    condition?: string
    label?: string
  }
}

export interface Pipeline {
  id: string
  name: string
  description?: string
  version: string
  nodes: PipelineNode[]
  edges: PipelineEdge[]
  variables: Record<string, any>
  secrets: string[]
  triggers: PipelineTrigger[]
  status: 'draft' | 'active' | 'paused' | 'archived'
  createdAt: Date
  updatedAt: Date
  createdBy: string
  tags: string[]
  metadata: {
    estimatedDuration?: number
    complexity?: 'simple' | 'moderate' | 'complex'
    category?: string
  }
}

export interface PipelineTrigger {
  id: string
  type: 'manual' | 'webhook' | 'schedule' | 'git_push' | 'git_pr'
  config: {
    schedule?: string // cron expression
    webhook?: {
      url: string
      secret?: string
    }
    git?: {
      repository: string
      branch?: string
      events: string[]
    }
  }
  enabled: boolean
}

export interface PipelineExecution {
  id: string
  pipelineId: string
  status: 'pending' | 'running' | 'success' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  duration?: number
  triggeredBy: {
    type: 'manual' | 'webhook' | 'schedule' | 'git'
    user?: string
    data?: Record<string, any>
  }
  nodeExecutions: NodeExecution[]
  logs: ExecutionLog[]
  artifacts: ExecutionArtifact[]
  variables: Record<string, any>
  error?: {
    message: string
    stack?: string
    nodeId?: string
  }
}

export interface NodeExecution {
  id: string
  nodeId: string
  status: 'pending' | 'running' | 'success' | 'failed' | 'skipped'
  startTime?: Date
  endTime?: Date
  duration?: number
  logs: string[]
  artifacts: string[]
  error?: {
    message: string
    code?: string
    details?: Record<string, any>
  }
  retryCount: number
  maxRetries: number
}

export interface ExecutionLog {
  id: string
  timestamp: Date
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  nodeId?: string
  source: string
  metadata?: Record<string, any>
}

export interface ExecutionArtifact {
  id: string
  name: string
  type: 'file' | 'image' | 'report' | 'binary'
  path: string
  size: number
  checksum: string
  nodeId?: string
  createdAt: Date
  metadata?: Record<string, any>
}

export interface DeploymentTarget {
  id: string
  name: string
  type: 'server' | 'cloud' | 'kubernetes' | 'docker' | 'ftp'
  status: 'connected' | 'disconnected' | 'error'
  config: {
    endpoint: string
    auth: {
      type: 'ssh' | 'token' | 'certificate' | 'password'
      credentials: Record<string, any>
    }
    environment?: string
    region?: string
    namespace?: string
  }
  healthCheck?: {
    url?: string
    interval: number
    timeout: number
    retries: number
  }
  lastDeployment?: Date
  deploymentCount: number
  createdAt: Date
  updatedAt: Date
}

export interface Secret {
  id: string
  name: string
  description?: string
  scope: 'global' | 'project' | 'environment'
  scopeValue?: string
  type: 'password' | 'token' | 'certificate' | 'key-value' | 'file'
  value: string // encrypted
  expiresAt?: Date
  isExpired: boolean
  rotationPolicy?: {
    enabled: boolean
    interval: number // days
    notifyBefore: number // days
  }
  accessLog: SecretAccess[]
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

export interface SecretAccess {
  id: string
  timestamp: Date
  user: string
  action: 'read' | 'write' | 'delete'
  source: string // pipeline, api, ui
  metadata?: Record<string, any>
}

export interface Template {
  id: string
  name: string
  description: string
  category: string
  tags: string[]
  version: string
  pipeline: Omit<Pipeline, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>
  parameters: TemplateParameter[]
  author: string
  isPublic: boolean
  downloads: number
  rating: number
  reviews: TemplateReview[]
  createdAt: Date
  updatedAt: Date
}

export interface TemplateParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect'
  description: string
  required: boolean
  defaultValue?: any
  options?: string[]
  validation?: {
    pattern?: string
    min?: number
    max?: number
  }
}

export interface TemplateReview {
  id: string
  userId: string
  rating: number
  comment?: string
  createdAt: Date
}

export interface PipelineValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  id: string
  type: 'syntax' | 'logic' | 'dependency' | 'security'
  message: string
  nodeId?: string
  severity: 'error' | 'warning'
  suggestion?: string
}

export interface ValidationWarning {
  id: string
  type: 'performance' | 'best-practice' | 'security'
  message: string
  nodeId?: string
  suggestion?: string
}
