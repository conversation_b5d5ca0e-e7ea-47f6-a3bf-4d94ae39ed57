import { useState, useCallback } from 'react'
import { use<PERSON>arams } from 'react-router-dom'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow'
import 'reactflow/dist/style.css'

import { Button } from '@/components/ui/button'
import { PipelineToolbar } from '@/components/pipeline/PipelineToolbar'
import { NodePalette } from '@/components/pipeline/NodePalette'
import { PipelineNode } from '@/components/pipeline/PipelineNode'
import { Save, Play, Settings, Share } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

const nodeTypes = {
  pipelineNode: PipelineNode,
}

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'pipelineNode',
    position: { x: 250, y: 100 },
    data: {
      label: 'Source Code',
      type: 'source',
      config: {
        repository: 'https://github.com/user/repo.git',
        branch: 'main',
      },
    },
  },
  {
    id: '2',
    type: 'pipelineNode',
    position: { x: 250, y: 250 },
    data: {
      label: 'Build',
      type: 'build',
      config: {
        command: 'npm run build',
        dockerfile: 'Dockerfile',
      },
    },
  },
]

const initialEdges: Edge[] = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    animated: true,
  },
]

export function PipelineDesigner() {
  const { pipelineId } = useParams()
  const { toast } = useToast()
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [isRunning, setIsRunning] = useState(false)

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
  }, [])

  const handleSave = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast({
        title: "Pipeline Saved",
        description: "Your pipeline has been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save pipeline. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleRun = async () => {
    setIsRunning(true)
    try {
      // Simulate pipeline execution
      await new Promise(resolve => setTimeout(resolve, 3000))
      toast({
        title: "Pipeline Started",
        description: "Your pipeline is now running.",
      })
    } catch (error) {
      toast({
        title: "Execution Failed",
        description: "Failed to start pipeline execution.",
        variant: "destructive",
      })
    } finally {
      setIsRunning(false)
    }
  }

  const addNode = (nodeType: string) => {
    const newNode: Node = {
      id: `${nodes.length + 1}`,
      type: 'pipelineNode',
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: nodeType.charAt(0).toUpperCase() + nodeType.slice(1),
        type: nodeType,
        config: {},
      },
    }
    setNodes((nds) => [...nds, newNode])
  }

  return (
    <div className="flex h-full">
      {/* Node Palette */}
      <div className="w-64 border-r border-border bg-card">
        <NodePalette onAddNode={addNode} />
      </div>

      {/* Main Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="h-16 border-b border-border bg-background flex items-center justify-between px-4">
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold">
              {pipelineId ? `Pipeline ${pipelineId}` : 'New Pipeline'}
            </h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={handleSave}>
              <Save className="mr-2 h-4 w-4" />
              Save
            </Button>
            <Button onClick={handleRun} disabled={isRunning}>
              <Play className="mr-2 h-4 w-4" />
              {isRunning ? 'Running...' : 'Run'}
            </Button>
            <Button variant="outline">
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* React Flow Canvas */}
        <div className="flex-1">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            fitView
            className="bg-background"
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          </ReactFlow>
        </div>
      </div>

      {/* Properties Panel */}
      {selectedNode && (
        <div className="w-80 border-l border-border bg-card p-4">
          <h3 className="text-lg font-semibold mb-4">Node Properties</h3>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Name</label>
              <input
                type="text"
                value={selectedNode.data.label}
                onChange={(e) => {
                  setNodes((nds) =>
                    nds.map((node) =>
                      node.id === selectedNode.id
                        ? { ...node, data: { ...node.data, label: e.target.value } }
                        : node
                    )
                  )
                }}
                className="w-full mt-1 px-3 py-2 border border-input rounded-md"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Type</label>
              <p className="text-sm text-muted-foreground mt-1 capitalize">
                {selectedNode.data.type}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium">Configuration</label>
              <textarea
                value={JSON.stringify(selectedNode.data.config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value)
                    setNodes((nds) =>
                      nds.map((node) =>
                        node.id === selectedNode.id
                          ? { ...node, data: { ...node.data, config } }
                          : node
                      )
                    )
                  } catch (error) {
                    // Invalid JSON, ignore
                  }
                }}
                rows={8}
                className="w-full mt-1 px-3 py-2 border border-input rounded-md font-mono text-sm"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
