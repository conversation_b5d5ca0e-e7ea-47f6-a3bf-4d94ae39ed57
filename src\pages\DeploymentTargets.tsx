import { useState } from 'react'
import { 
  Plus, 
  Server, 
  Cloud, 
  Database, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { formatDate } from '@/lib/utils'

interface DeploymentTarget {
  id: string
  name: string
  type: 'server' | 'cloud' | 'kubernetes' | 'docker'
  status: 'connected' | 'disconnected' | 'error'
  endpoint: string
  authMethod: 'ssh' | 'token' | 'certificate'
  lastDeployment?: Date
  deploymentCount: number
  description?: string
}

export function DeploymentTargets() {
  const { toast } = useToast()
  const [targets, setTargets] = useState<DeploymentTarget[]>([
    {
      id: '1',
      name: 'Production Server',
      type: 'server',
      status: 'connected',
      endpoint: '*************',
      authMethod: 'ssh',
      lastDeployment: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      deploymentCount: 45,
      description: 'Main production server for web application'
    },
    {
      id: '2',
      name: 'AWS ECS Cluster',
      type: 'cloud',
      status: 'connected',
      endpoint: 'ecs.us-east-1.amazonaws.com',
      authMethod: 'token',
      lastDeployment: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      deploymentCount: 128,
      description: 'AWS ECS cluster for containerized applications'
    },
    {
      id: '3',
      name: 'Staging Environment',
      type: 'kubernetes',
      status: 'disconnected',
      endpoint: 'k8s-staging.company.com',
      authMethod: 'certificate',
      lastDeployment: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      deploymentCount: 23,
      description: 'Kubernetes staging environment'
    },
    {
      id: '4',
      name: 'Docker Registry',
      type: 'docker',
      status: 'error',
      endpoint: 'registry.company.com',
      authMethod: 'token',
      deploymentCount: 67,
      description: 'Private Docker registry'
    }
  ])

  const [showAddDialog, setShowAddDialog] = useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-gray-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'server':
        return <Server className="h-5 w-5" />
      case 'cloud':
        return <Cloud className="h-5 w-5" />
      case 'kubernetes':
        return <Server className="h-5 w-5" />
      case 'docker':
        return <Database className="h-5 w-5" />
      default:
        return <Server className="h-5 w-5" />
    }
  }

  const handleTestConnection = async (targetId: string) => {
    toast({
      title: "Testing Connection",
      description: "Testing connection to deployment target...",
    })

    // Simulate connection test
    setTimeout(() => {
      toast({
        title: "Connection Test",
        description: "Connection test completed successfully.",
      })
    }, 2000)
  }

  const handleDeleteTarget = (targetId: string) => {
    setTargets(targets.filter(t => t.id !== targetId))
    toast({
      title: "Target Deleted",
      description: "Deployment target has been removed.",
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Deployment Targets</h1>
          <p className="text-muted-foreground">
            Manage your deployment destinations and infrastructure.
          </p>
        </div>
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Target
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Targets</p>
              <p className="text-2xl font-bold">{targets.length}</p>
            </div>
            <Server className="h-8 w-8 text-muted-foreground" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Connected</p>
              <p className="text-2xl font-bold text-green-500">
                {targets.filter(t => t.status === 'connected').length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Disconnected</p>
              <p className="text-2xl font-bold text-gray-500">
                {targets.filter(t => t.status === 'disconnected').length}
              </p>
            </div>
            <XCircle className="h-8 w-8 text-gray-500" />
          </div>
        </div>

        <div className="rounded-lg border bg-card p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Errors</p>
              <p className="text-2xl font-bold text-red-500">
                {targets.filter(t => t.status === 'error').length}
              </p>
            </div>
            <AlertCircle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Targets List */}
      <div className="rounded-lg border bg-card">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Deployment Targets</h2>
          <div className="space-y-4">
            {targets.map((target) => (
              <div key={target.id} className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-accent">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(target.type)}
                    {getStatusIcon(target.status)}
                  </div>
                  <div>
                    <h3 className="font-medium">{target.name}</h3>
                    <p className="text-sm text-muted-foreground">{target.endpoint}</p>
                    {target.description && (
                      <p className="text-xs text-muted-foreground mt-1">{target.description}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium">{target.deploymentCount} deployments</p>
                    {target.lastDeployment && (
                      <p className="text-xs text-muted-foreground">
                        Last: {formatDate(target.lastDeployment)}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleTestConnection(target.id)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteTarget(target.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
